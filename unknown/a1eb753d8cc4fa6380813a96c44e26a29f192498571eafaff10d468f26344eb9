using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RazeWinComTr.Areas.Admin.DbModel
{
    [Table("TRADE")]
    public class Trade
    {
        [Key]
        [Column("ID")]
        public int Id { get; set; }

        [Required]
        [Column("TYPE")]
        public TradeType Type { get; set; }

        [Required]
        [Column("USER_ID")]
        public int UserId { get; set; }

        [Required]
        [Column("COIN_ID")]
        public int CoinId { get; set; }

        [Required]
        [Column("COIN_RATE", TypeName = "decimal(20,8)")]
        public decimal CoinRate { get; set; }

        [Required]
        [Column("COIN_AMOUNT", TypeName = "decimal(20,8)")]
        public decimal CoinAmount { get; set; }

        [Required]
        [Column("TRY_AMOUNT", TypeName = "decimal(20,2)")]
        public decimal TryAmount { get; set; }

        [Required]
        [Column("PREVIOUS_COIN_BALANCE", TypeName = "decimal(20,8)")]
        public decimal PreviousCoinBalance { get; set; }

        [Required]
        [Column("NEW_COIN_BALANCE", TypeName = "decimal(20,8)")]
        public decimal NewCoinBalance { get; set; }

        [Required]
        [Column("PREVIOUS_BALANCE", TypeName = "decimal(20,2)")]
        public decimal PreviousBalance { get; set; }

        [Required]
        [Column("NEW_BALANCE", TypeName = "decimal(20,2)")]
        public decimal NewBalance { get; set; }

        [Required]
        [Column("PREVIOUS_WALLET_BALANCE", TypeName = "decimal(20,8)")]
        public decimal PreviousWalletBalance { get; set; }

        [Required]
        [Column("NEW_WALLET_BALANCE", TypeName = "decimal(20,8)")]
        public decimal NewWalletBalance { get; set; }

        [Required]
        [Column("CR_DATE", TypeName = "datetime")]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        [Required]
        [Column("STATUS")]
        public bool IsActive { get; set; } = true;

        [Column("REFERRAL_REWARD_ID")]
        public int? ReferralRewardId { get; set; }

        [Column("RZW_SAVINGS_ACCOUNT_ID")]
        public int? RzwSavingsAccountId { get; set; }

        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        [ForeignKey("CoinId")]
        public virtual Market Coin { get; set; } = null!;

        [ForeignKey("ReferralRewardId")]
        public virtual ReferralReward? ReferralReward { get; set; }

        [ForeignKey("RzwSavingsAccountId")]
        public virtual RzwSavingsAccount? RzwSavingsAccount { get; set; }
    }

    public enum TradeType
    {
        Buy = 0,            // alış - coin artışı (TRY azalır, coin artar)
        Sell = 1,           // satış - coin azalışı (coin azalır, TRY artar)
        PackageBonus = 2,   // paket satın alımında verilen bonus - coin artışı
        ReferralReward = 3,  // yönlendirme ödülü - coin artışı

        CryptoDeposit = 4, // Kripto Para Yatırma - coin artışı
        CryptoWidthdraw = 5, // Kripto Para Çekme - coin azalışı

        // RZW Savings types
        RzwSavingsAccountOpening = 6,      // vadeli hesaba yatırma - coin azalışı (available balance'dan locked balance'a)
        RzwSavingsAccountClosing = 7,   // vadeli hesaptan çekme - coin artışı (locked balance'dan available balance'a)
        RzwSavingsInterestPayment = 8,     // faiz ödemesi - coin artışı
        RzwSavingsEarlyClosing = 9, // erken çekim - coin artışı (locked balance'dan available balance'a)
        //RzwSavingsMaturity=10      // vade dolma - coin artışı (locked balance'dan available balance'a)
    }

    public static class TradeTypeExtensions
    {
        /// <summary>
        /// Trade tipinin coin miktarı açısından para girişi mi para çıkışı mı olduğunu belirler
        /// Amount alanı coin miktarını gösterdiği için, coin artışı = para girişi (yeşil), coin azalışı = para çıkışı (kırmızı)
        /// </summary>
        /// <param name="tradeType">Trade tipi</param>
        /// <returns>True: Coin artışı/Para girişi (yeşil), False: Coin azalışı/Para çıkışı (kırmızı)</returns>
        public static bool IsCoinInflow(this TradeType tradeType)
        {
            return tradeType switch
            {
                // Coin artışı (yeşil) - kullanıcının coin miktarı artar
                TradeType.Buy => true,                    // TL vererek coin alıyor
                TradeType.PackageBonus => true,           // Bonus coin alıyor
                TradeType.ReferralReward => true,         // Ödül coin alıyor
                TradeType.CryptoDeposit => true,          // Dışarıdan coin yatırıyor
                TradeType.RzwSavingsAccountClosing => true,   // Vadeli hesaptan coin çekiyor
                TradeType.RzwSavingsInterestPayment => true,     // Faiz coin alıyor
                TradeType.RzwSavingsEarlyClosing => true, // Erken çekim ile coin alıyor
                //TradeType.RzwSavingsMaturity => true,     // Vade sonunda coin alıyor

                // Coin azalışı (kırmızı) - kullanıcının coin miktarı azalır
                TradeType.Sell => false,                  // Coin vererek TL alıyor
                TradeType.CryptoWidthdraw => false,       // Coin çekiyor (dışarıya)
                TradeType.RzwSavingsAccountOpening => false,     // Vadeli hesaba coin yatırıyor

                _ => true // Varsayılan olarak para girişi kabul et
            };
        }


        public static string GetLocalizationKey(this TradeType tradeType)
        {
            return tradeType switch
            {
                TradeType.Buy => "Buy",
                TradeType.PackageBonus => "Package Bonus",
                TradeType.ReferralReward => "Referral Reward",
                TradeType.CryptoDeposit => "Crypto Deposit",
                TradeType.RzwSavingsAccountClosing => "Savings Account Closing",
                TradeType.RzwSavingsInterestPayment => "Savings Interest",
                TradeType.RzwSavingsEarlyClosing => "Savings Account Early Closing",
                //TradeType.RzwSavingsMaturity => "Savings Maturity",

                TradeType.Sell => "Sell",
                TradeType.CryptoWidthdraw => "Crypto Widthdraw",
                TradeType.RzwSavingsAccountOpening => "Savings Account Opening",

                _ => tradeType.ToString()
            };
        }
    }
}
