using RazeWinComTr.Areas.Admin.Enums;

namespace RazeWinComTr.Areas.Admin.Models;

/// <summary>
/// Generic verification status information
/// </summary>
public class VerificationStatus
{
    public bool IsVerified { get; set; }
    public DateTime? VerifiedDate { get; set; }
    public bool CanSendVerification { get; set; }
    public TimeSpan? TimeUntilNextVerification { get; set; }
    public int TodayAttempts { get; set; }
    public int MaxDailyAttempts { get; set; } = 5;
    public int MinutesBetweenAttempts { get; set; } = 5;
    public string TargetValue { get; set; } = string.Empty;
    public VerificationType VerificationType { get; set; }
}
