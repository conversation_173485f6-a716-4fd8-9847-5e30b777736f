using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Models;

namespace RazeWinComTr.RewardTests.Unit.Services
{
    public class RzwBalanceManagementServiceTests : IDisposable
    {
        private readonly AppDbContext _context;
        private readonly Mock<IWalletService> _mockWalletService;
        private readonly Mock<ITokenPriceService> _mockTokenPriceService;
        private readonly Mock<ITradeService> _mockTradeService;
        private readonly RzwWalletBalanceManagementService _service;

        public RzwBalanceManagementServiceTests()
        {
            // Setup in-memory database
            var options = new DbContextOptionsBuilder<AppDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .ConfigureWarnings(warnings => warnings.Ignore(InMemoryEventId.TransactionIgnoredWarning))
                .Options;
            _context = new AppDbContext(options);

            // Setup mocks
            _mockWalletService = new Mock<IWalletService>(MockBehavior.Strict);
            _mockTokenPriceService = new Mock<ITokenPriceService>(MockBehavior.Strict);
            _mockTradeService = new Mock<ITradeService>(MockBehavior.Strict);

            // Setup TokenPriceService mock to return RZW token info
            var rzwTokenInfo = new RzwTokenInfo
            {
                TokenId = 1,
                BuyPrice = 1.0m,
                SellPrice = 0.95m
            };
            _mockTokenPriceService.Setup(x => x.GetRzwTokenInfoAsync())
                .ReturnsAsync(rzwTokenInfo);

            // Create service instance
            _service = new RzwWalletBalanceManagementService(
                _context,
                _mockWalletService.Object,
                _mockTokenPriceService.Object,
                _mockTradeService.Object
            );
        }

        public void Dispose()
        {
            _context.Dispose();
        }

        #region Lock RZW for Savings Tests

        [Fact]
        public async Task LockRzwForSavingsAsync_WithSufficientBalance_ShouldLockBalanceAndCreateTrade()
        {
            // Setup mocks
            _mockWalletService.Setup(x => x.GetUserAvailableBalanceAsync(1, 1))
                .ReturnsAsync(10000m);
            _mockWalletService.Setup(x => x.LockBalanceAsync(1, It.IsAny<RzwTokenInfo>(), 5000m, It.IsAny<AppDbContext>()))
                .ReturnsAsync(true);
            // Setup TradeService mock to allow CreateAsync
            _mockTradeService.Setup(x => x.CreateAsync(It.IsAny<Trade>(), It.IsAny<AppDbContext>()))
                .ReturnsAsync((Trade trade, AppDbContext ctx) => trade);

            // Act
            var result = await _service.LockRzwForSavingsAsync(1, 5000m, "Test savings lock", _context, 1);

            // Assert
            Assert.True(result);

            // Verify mock calls
            _mockWalletService.Verify(x => x.GetUserAvailableBalanceAsync(1, 1), Times.Once);
            _mockWalletService.Verify(x => x.LockBalanceAsync(1, It.IsAny<RzwTokenInfo>(), 5000m, It.IsAny<AppDbContext>()), Times.Once);

            // Verify that TradeService.CreateAsync was called
            _mockTradeService.Verify(x => x.CreateAsync(It.Is<Trade>(t =>
                t.UserId == 1 &&
                t.CoinAmount == -5000m && // Amount is negative for lock operation
                t.Type == TradeType.RzwSavingsAccountOpening),
                It.IsAny<AppDbContext>()), Times.Once);
        }

        [Fact]
        public async Task LockRzwForSavingsAsync_WithInsufficientBalance_ShouldReturnFalse()
        {
            // Setup mocks
            _mockWalletService.Setup(x => x.GetUserAvailableBalanceAsync(1, 1))
                .ReturnsAsync(3000m); // Less than required 5000m

            // Act
            var result = await _service.LockRzwForSavingsAsync(1, 5000m, "Test savings lock", _context, 1);

            // Assert
            Assert.False(result);

            // Verify mock calls
            _mockWalletService.Verify(x => x.GetUserAvailableBalanceAsync(1, 1), Times.Once);
            _mockWalletService.Verify(x => x.LockBalanceAsync(It.IsAny<int>(), It.IsAny<RzwTokenInfo>(), It.IsAny<decimal>(), It.IsAny<AppDbContext>()), Times.Never);

            var trade = await _context.Trades
                .FirstOrDefaultAsync(t => t.UserId == 1 && t.Type == TradeType.RzwSavingsAccountOpening);
            Assert.Null(trade); // No trade should be created
        }

        [Fact]
        public async Task LockRzwForSavingsAsync_WithNoRzwWallet_ShouldReturnFalse()
        {
            // Setup mocks - no wallet means 0 balance
            _mockWalletService.Setup(x => x.GetUserAvailableBalanceAsync(1, 1))
                .ReturnsAsync(0m);

            // Act
            var result = await _service.LockRzwForSavingsAsync(1, 5000m, "Test savings lock", _context, 1);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task LockRzwForSavingsAsync_WithZeroAmount_ShouldReturnFalse()
        {
            // Act
            var result = await _service.LockRzwForSavingsAsync(1, 0m, "Test savings lock", _context, 1);

            // Assert
            Assert.False(result);
        }

        #endregion

        #region Unlock RZW from Savings Tests

        [Fact]
        public async Task UnlockRzwFromSavingsAsync_WithSufficientLockedBalance_ShouldUnlockBalanceAndCreateTrade()
        {
            var rzwTokenInfo = new RzwTokenInfo
            {
                TokenId = 1,
                BuyPrice = 1.0m,
                SellPrice = 0.95m
            };
            var expectedTrade = new Trade
            {
                UserId = 1,
                CoinId = 1,
                CoinAmount = 5000m, // Amount is positive for unlock operation
                Type = TradeType.RzwSavingsAccountClosing,
                CreatedDate = DateTime.UtcNow
            };
            _mockTokenPriceService.Setup(x => x.GetRzwTokenInfoAsync()).ReturnsAsync(rzwTokenInfo);

            // Setup mocks
            _mockWalletService.Setup(x => x.UnlockBalanceAsync(1, rzwTokenInfo, 5000m, It.IsAny<AppDbContext>())).ReturnsAsync(true);
            _mockTradeService.Setup(x => x.CreateAsync(It.IsAny<Trade>(), It.IsAny<AppDbContext>())).ReturnsAsync((Trade trade, AppDbContext ctx) => trade);
            // Act
            var result = await _service.UnlockRzwFromSavingsAsync(1, 5000m, "Test savings unlock", TradeType.RzwSavingsAccountClosing, _context, 1);

            // Assert
            Assert.True(result);

            _mockTokenPriceService.VerifyAll();
            _mockWalletService.VerifyAll();
            _mockTradeService.VerifyAll();
            // Verify mock calls
            _mockWalletService.Verify(x => x.UnlockBalanceAsync(1, rzwTokenInfo, 5000m, It.IsAny<AppDbContext>()), Times.Once);

            // Verify that TradeService.CreateAsync was called
            _mockTradeService.Verify(x => x.CreateAsync(It.Is<Trade>(t =>
                t.UserId == expectedTrade.UserId &&
                t.CoinAmount == expectedTrade.CoinAmount && // Amount is positive for unlock operation
                t.Type == expectedTrade.Type),
                It.IsAny<AppDbContext>()), Times.Once);
        }

        [Fact]
        public async Task UnlockRzwFromSavingsAsync_WithInsufficientLockedBalance_ShouldReturnFalse()
        {
            // Arrange
            var user = new User
            {
                UserId = 1,
                Email = "<EMAIL>",
                IdentityNumber = "12345678901",
                Name = "Test",
                Surname = "User",
                PhoneNumber = "5551234567"
            };
            var market = new Market { Id = 1, Coin = "RZW", Name = "RazeWin Token", ShortName = "RZW", PairCode = "RZWTRY" };
            var wallet = new Wallet
            {
                Id = 1,
                UserId = 1,
                CoinId = 1,
                Balance = 5000m,
                LockedBalance = 3000m // Less than required
            };
            var rzwTokenInfo = new RzwTokenInfo
            {
                TokenId = 1,
                BuyPrice = 1.0m,
                SellPrice = 0.95m
            };
            await _context.Users.AddAsync(user);
            await _context.Markets.AddAsync(market);
            await _context.Wallets.AddAsync(wallet);
            await _context.SaveChangesAsync();

            _mockTokenPriceService.Setup(x => x.GetRzwTokenInfoAsync()).ReturnsAsync(rzwTokenInfo);
            _mockWalletService.Setup(x => x.UnlockBalanceAsync(1, rzwTokenInfo, 5000m, It.IsAny<AppDbContext?>())).ReturnsAsync(false); // Simulate unlock failure
            // Act
            var result = await _service.UnlockRzwFromSavingsAsync(1, 5000m, "Test savings unlock", TradeType.RzwSavingsAccountClosing, _context, 1);

            // Assert
            Assert.False(result);

            var wallet_after = await _context.Wallets.FirstAsync(w => w.UserId == 1 && w.CoinId == 1);
            Assert.Equal(5000m, wallet_after.Balance); // Unchanged
            Assert.Equal(3000m, wallet_after.LockedBalance); // Unchanged

            _mockTokenPriceService.VerifyAll();
            _mockWalletService.VerifyAll();
        }

        #endregion

        #region Add RZW Interest Tests

        [Fact]
        public async Task AddRzwInterestAsync_WithValidAmount_ShouldAddToAvailableBalanceAndCreateTrade()
        {
            // Setup mocks
            var expectedWallet = new Wallet
            {
                Id = 1,
                UserId = 1,
                CoinId = 1,
                Balance = 5025.5m,
                LockedBalance = 0m
            };
            _mockWalletService.Setup(x => x.AddAvailableBalanceAsync(1, It.IsAny<RzwTokenInfo>(), 25.5m, TradeType.RzwSavingsInterestPayment, It.IsAny<AppDbContext>()))
                .ReturnsAsync(expectedWallet);

            // Act
            var result = await _service.AddRzwInterestAsync(1, 1, 25.5m, "Test interest payment", _context);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(5025.5m, result.Balance);

            // Verify mock calls
            _mockWalletService.Verify(x => x.AddAvailableBalanceAsync(1, It.IsAny<RzwTokenInfo>(), 25.5m, TradeType.RzwSavingsInterestPayment, It.IsAny<AppDbContext>()), Times.Once);

            var trade = await _context.Trades
                .FirstOrDefaultAsync(t => t.UserId == 1 && t.CoinAmount == 25.5m && t.Type == TradeType.RzwSavingsInterestPayment);
            Assert.NotNull(trade);
            Assert.Equal(1, trade.CoinId);
        }

        [Fact]
        public async Task AddRzwInterestAsync_WithNoRzwWallet_ShouldCreateWalletAndAddInterest()
        {
            // Setup mocks - new wallet created
            var expectedWallet = new Wallet
            {
                Id = 1,
                UserId = 1,
                CoinId = 1,
                Balance = 25.5m,
                LockedBalance = 0m
            };
            _mockWalletService.Setup(x => x.AddAvailableBalanceAsync(1, It.IsAny<RzwTokenInfo>(), 25.5m, TradeType.RzwSavingsInterestPayment, It.IsAny<AppDbContext>()))
                .ReturnsAsync(expectedWallet);

            // Act
            var result = await _service.AddRzwInterestAsync(1, 1, 25.5m, "Test interest payment", _context);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(25.5m, result.Balance);

            var trade = await _context.Trades
                .FirstOrDefaultAsync(t => t.UserId == 1 && t.CoinAmount == 25.5m && t.Type == TradeType.RzwSavingsInterestPayment);
            Assert.NotNull(trade);
        }

        [Fact]
        public async Task AddRzwInterestAsync_WithZeroAmount_ShouldAddZeroInterest()
        {
            // Setup mocks
            var expectedWallet = new Wallet
            {
                Id = 1,
                UserId = 1,
                CoinId = 1,
                Balance = 0m,
                LockedBalance = 0m
            };
            _mockWalletService.Setup(x => x.AddAvailableBalanceAsync(1, It.IsAny<RzwTokenInfo>(), 0m, TradeType.RzwSavingsInterestPayment, It.IsAny<AppDbContext>()))
                .ReturnsAsync(expectedWallet);

            // Act
            var result = await _service.AddRzwInterestAsync(1, 1, 0m, "Test interest payment", _context);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(0m, result.Balance);
        }

        [Fact]
        public async Task AddRzwInterestAsync_WithNegativeAmount_ShouldAddNegativeInterest()
        {
            // Setup mocks
            var expectedWallet = new Wallet
            {
                Id = 1,
                UserId = 1,
                CoinId = 1,
                Balance = -10m,
                LockedBalance = 0m
            };
            _mockWalletService.Setup(x => x.AddAvailableBalanceAsync(1, It.IsAny<RzwTokenInfo>(), -10m, TradeType.RzwSavingsInterestPayment, It.IsAny<AppDbContext>()))
                .ReturnsAsync(expectedWallet);

            // Act
            var result = await _service.AddRzwInterestAsync(1, 1, -10m, "Test interest payment", _context);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(-10m, result.Balance);
        }

        #endregion

        #region Get RZW Balance Tests

        [Fact]
        public async Task GetRzwBalanceInfoAsync_WithExistingWallet_ShouldReturnCorrectBalance()
        {
            // Arrange
            var user = new User
            {
                UserId = 1,
                Email = "<EMAIL>",
                IdentityNumber = "12345678901",
                Name = "Test",
                Surname = "User",
                PhoneNumber = "5551234567"
            };
            var market = new Market { Id = 1, Coin = "RZW", Name = "RazeWin Token", ShortName = "RZW", PairCode = "RZWTRY" };
            var wallet = new Wallet
            {
                Id = 1,
                UserId = 1,
                CoinId = 1,
                Balance = 7500.25m,
                LockedBalance = 2500m
            };

            await _context.Users.AddAsync(user);
            await _context.Markets.AddAsync(market);
            await _context.Wallets.AddAsync(wallet);
            await _context.SaveChangesAsync();

            // Setup mock
            _mockWalletService.Setup(x => x.GetWalletBalanceInfoAsync(1, 1))
                .ReturnsAsync(new WalletBalanceInfo
                {
                    AvailableBalance = 7500.25m,
                    LockedBalance = 2500m,
                    ActiveSavingsAccountCount = 0
                });

            // Act
            var result = await _service.GetRzwBalanceInfoAsync(1);

            // Assert
            Assert.Equal(7500.25m, result.AvailableRzw);
            Assert.Equal(2500m, result.LockedRzw);
        }

        [Fact]
        public async Task GetRzwBalanceInfoAsync_WithNoWallet_ShouldReturnZeroBalance()
        {
            // Setup mock
            _mockWalletService.Setup(x => x.GetWalletBalanceInfoAsync(999, 1))
                .ReturnsAsync(new WalletBalanceInfo
                {
                    AvailableBalance = 0m,
                    LockedBalance = 0m,
                    ActiveSavingsAccountCount = 0
                });

            // Act
            var result = await _service.GetRzwBalanceInfoAsync(999);

            // Assert
            Assert.Equal(0m, result.AvailableRzw);
            Assert.Equal(0m, result.LockedRzw);
        }

        [Fact]
        public async Task HasSufficientAvailableRzwAsync_WithSufficientBalance_ShouldReturnTrue()
        {
            // Setup mock
            _mockWalletService.Setup(x => x.GetUserAvailableBalanceAsync(1, 1))
                .ReturnsAsync(7500.25m);

            // Act
            var result = await _service.HasSufficientAvailableRzwAsync(1, 5000m);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task HasSufficientAvailableRzwAsync_WithInsufficientBalance_ShouldReturnFalse()
        {
            // Setup mock
            _mockWalletService.Setup(x => x.GetUserAvailableBalanceAsync(999, 1))
                .ReturnsAsync(0m);

            // Act
            var result = await _service.HasSufficientAvailableRzwAsync(999, 5000m);

            // Assert
            Assert.False(result);
        }

        #endregion
    }
}
