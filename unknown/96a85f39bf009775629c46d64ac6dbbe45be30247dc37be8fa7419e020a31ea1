﻿using Microsoft.AspNetCore.Mvc;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Models;

namespace RazeWinComTr.Controllers;

[Route("api/[controller]")]
[ApiController]
public class EmailTestController : ControllerBase
{
    private readonly IEmailHelper _emailHelper;
    private readonly ILogger<EmailTestController> _logger;

    public EmailTestController(
        IEmailHelper emailHelper,
        ILogger<EmailTestController> logger)
    {
        _emailHelper = emailHelper;
        _logger = logger;
    }

    [HttpGet("settings")]
    public async Task<IActionResult> GetSettings()
    {
        try
        {
            var settings = await _emailHelper.GetSmtpSettingsAsync();
            // Mask the password for security
            settings.Password = "********";
            return Ok(settings);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting SMTP settings");
            return StatusCode(500, "Error getting SMTP settings");
        }
    }



    [HttpGet("send-test")]
    public async Task<IActionResult> SendTestEmail(string to)
    {
        try
        {
            if (string.IsNullOrEmpty(to))
            {
                return BadRequest("Recipient email address is required");
            }

            var result = await _emailHelper.SendSimpleEmailAsync(
                to,
                $"Test Email from Test Platform {DateTime.Now}",
                "<h1>This is a test email</h1><p>This email was sent from the application using the configured email provider.</p>",
                true);

            if (result)
            {
                return Ok("Email sent successfully");
            }
            else
            {
                return StatusCode(500, "Failed to send email");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending test email");
            return StatusCode(500, $"Error sending test email: {ex.Message}");
        }
    }

    [HttpPost("send-test")]
    public async Task<IActionResult> SendTestEmailPost([FromBody] TestEmailRequest request)
    {
        try
        {
            if (string.IsNullOrEmpty(request.To))
            {
                return BadRequest("Recipient email address is required");
            }

            var result = await _emailHelper.SendSimpleEmailAsync(
                request.To,
                $"Test Email from Test Platform {DateTime.Now}",
                "<h1>This is a test email</h1><p>This email was sent from the application using the configured email provider.</p>",
                true);

            if (result)
            {
                return Ok("Email sent successfully");
            }
            else
            {
                return StatusCode(500, "Failed to send email");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending test email");
            return StatusCode(500, $"Error sending test email: {ex.Message}");
        }
    }



    [HttpPost("send-complex")]
    public async Task<IActionResult> SendComplexEmail([FromBody] ComplexEmailRequest request)
    {
        try
        {
            if (request.ToAddresses == null || !request.ToAddresses.Any())
            {
                return BadRequest("At least one recipient email address is required");
            }

            var emailMessage = new EmailMessage
            {
                Subject = request.Subject,
                Body = request.Body,
                IsHtml = request.IsHtml,
                ToAddresses = request.ToAddresses.Select(email => new EmailAddress(email)).ToList()
            };

            if (request.CcAddresses != null)
            {
                emailMessage.CcAddresses = request.CcAddresses.Select(email => new EmailAddress(email)).ToList();
            }

            if (request.BccAddresses != null)
            {
                emailMessage.BccAddresses = request.BccAddresses.Select(email => new EmailAddress(email)).ToList();
            }

            var result = await _emailHelper.SendEmailAsync(emailMessage);

            if (result)
            {
                return Ok("Email sent successfully");
            }
            else
            {
                return StatusCode(500, "Failed to send email");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending complex email");
            return StatusCode(500, $"Error sending complex email: {ex.Message}");
        }
    }


}

public class TestEmailRequest
{
    public string To { get; set; } = string.Empty;
}

public class ComplexEmailRequest
{
    public List<string> ToAddresses { get; set; } = new List<string>();
    public List<string>? CcAddresses { get; set; }
    public List<string>? BccAddresses { get; set; }
    public string Subject { get; set; } = string.Empty;
    public string Body { get; set; } = string.Empty;
    public bool IsHtml { get; set; } = true;
}
