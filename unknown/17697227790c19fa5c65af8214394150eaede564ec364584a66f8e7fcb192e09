using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using RazeWinComTr.Areas.Admin.Services.Interfaces;

namespace RazeWinComTr.BackgroundServices;

/// <summary>
/// Background service for RZW savings system that handles:
/// - Maturity checks and processing (with total interest payment)
/// - Automated savings account management
/// Note: Daily interest payments removed for better performance - interest paid only at maturity
/// </summary>
public class RzwSavingsBackgroundService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<RzwSavingsBackgroundService> _logger;
    private readonly IConfiguration _configuration;
    
    // Configuration values
    private readonly TimeSpan _processingInterval;
    private readonly TimeSpan _processingTime;
    private readonly bool _enableProcessing;
    private readonly int _batchSize;

    public RzwSavingsBackgroundService(
        IServiceProvider serviceProvider,
        ILogger<RzwSavingsBackgroundService> logger,
        IConfiguration configuration)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _configuration = configuration;

        // Load configuration
        var config = _configuration.GetSection("RzwSavings:BackgroundService");
        var processingHour = config.GetValue<int>("ProcessingHour", 2);
        var processingIntervalMinutes = config.GetValue<int>("ProcessingIntervalMinutes", 60);
        _batchSize = config.GetValue<int>("BatchSize", 100);
        _enableProcessing = config.GetValue<bool>("EnableProcessing", true);

        _processingTime = TimeSpan.FromHours(processingHour);
        _processingInterval = TimeSpan.FromMinutes(processingIntervalMinutes);
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("RZW Savings Background Service started. Processing time: {ProcessingTime}, Interval: {Interval}, Enabled: {Enabled}",
            _processingTime, _processingInterval, _enableProcessing);

        if (!_enableProcessing)
        {
            _logger.LogInformation("RZW Savings Background Service is disabled via configuration");
            return;
        }

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await WaitForProcessingTime(stoppingToken);
                await ProcessDailyOperationsAsync();
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("RZW Savings Background Service is stopping");
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in RZW savings background processing");
            }

            // Wait for next processing cycle
            await Task.Delay(_processingInterval, stoppingToken);
        }

        _logger.LogInformation("RZW Savings Background Service stopped");
    }

    /// <summary>
    /// Waits until the configured processing time (e.g., 02:00 AM)
    /// </summary>
    private async Task WaitForProcessingTime(CancellationToken cancellationToken)
    {
        var now = DateTime.Now;
        var targetTime = now.Date.Add(_processingTime);

        // If target time has passed today, schedule for tomorrow
        if (now > targetTime)
            targetTime = targetTime.AddDays(1);

        var delay = targetTime - now;
        if (delay > TimeSpan.Zero)
        {
            _logger.LogInformation("Waiting until {TargetTime} for daily RZW savings processing", targetTime);
            await Task.Delay(delay, cancellationToken);
        }
    }

    /// <summary>
    /// Processes daily operations: maturity checks only (no daily interest payments)
    /// Interest is paid only when accounts reach maturity for better performance and logic
    /// </summary>
    public async Task ProcessDailyOperationsAsync()
    {
        using var scope = _serviceProvider.CreateScope();
        var savingsService = scope.ServiceProvider.GetRequiredService<IRzwSavingsService>();

        try
        {
            _logger.LogInformation("Starting daily RZW savings operations (maturity checks only)");

            // Process maturity checks (includes total interest payment and auto-renew)
            var processedMaturity = await ProcessMaturityChecksAsync(savingsService);
            _logger.LogInformation("Maturity processing completed. Processed accounts: {ProcessedMaturity}", processedMaturity);

            _logger.LogInformation("Daily RZW savings operations completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during daily RZW savings operations");
            throw;
        }
    }

    /// <summary>
    /// Processes maturity checks for all eligible accounts with interest payment and auto-renew
    /// </summary>
    private async Task<int> ProcessMaturityChecksAsync(IRzwSavingsService savingsService)
    {
        var processedCount = 0;

        try
        {
            var maturedAccounts = await savingsService.GetMaturedAccountsAsync();
            _logger.LogInformation("Found {Count} matured accounts to process", maturedAccounts.Count);

            foreach (var account in maturedAccounts)
            {
                var success = await ProcessAccountSafelyAsync(account, async acc =>
                {
                    var (success, message) = await savingsService.ProcessMaturityWithInterestAndAutoRenewAsync(acc.Id);
                    if (success)
                    {
                        _logger.LogInformation("Processed maturity for account {AccountId}, User {UserId}, Amount {Amount:N8} RZW",
                            acc.Id, acc.UserId, acc.RzwAmount);
                        return true;
                    }
                    else
                    {
                        _logger.LogWarning("Failed to process maturity for account {AccountId}: {Message}",
                            acc.Id, message);
                        return false;
                    }
                });

                if (success) processedCount++;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during maturity checks processing");
            throw;
        }

        return processedCount;
    }

    /// <summary>
    /// Safely processes an account with error handling
    /// </summary>
    private async Task<bool> ProcessAccountSafelyAsync<T>(T account, Func<T, Task<bool>> processor)
    {
        try
        {
            return await processor(account);
        }
        catch (Exception ex)
        {
            var accountId = account?.GetType().GetProperty("Id")?.GetValue(account);
            var userId = account?.GetType().GetProperty("UserId")?.GetValue(account);
            
            _logger.LogError(ex, "Error processing account {AccountId} for user {UserId}",
                accountId, userId);
            return false;
        }
    }
}
