@page
@model RazeWinComTr.Areas.MyAccount.Pages.RzwSavings.DetailsModel
@using Microsoft.Extensions.Localization
@using RazeWinComTr.Areas.Admin.Helpers
@inject IStringLocalizer<SharedResource> Localizer
@{
    ViewData["Title"] = Localizer["Savings Account Details"];
}

@if (Model.AlertMessage != null)
{
    @await Html.PartialAsync("_SweetAlert2Script", Model.AlertMessage)
}
@section Styles {
    <link rel="stylesheet" href="/css/rzw-savings-details.css">
    <style>
        .account-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .progress-custom {
            height: 20px;
            border-radius: 10px;
            background-color: rgba(255, 255, 255, 0.2);
            position: relative !important;
            width: 100%;
            overflow: hidden;
        }

        .progress-custom .progress-bar {
            border-radius: 10px;
            background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
            position: relative !important;
            height: 100%;
        }
        
        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            height: 100%;
        }
        
        .stat-card .icon {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .stat-card .value {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-card .label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .interest-table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .early-withdraw-warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
    </style>
}

<!-- Content Header -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@Localizer["Savings Account Details"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/MyAccount/Dashboard">@Localizer["Home"]</a></li>
                    <li class="breadcrumb-item"><a href="/MyAccount/RzwSavings">@Localizer["My Savings"]</a></li>
                    <li class="breadcrumb-item active">@Localizer["Details"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Zero Interest Rate Warning -->
@if (ViewData["HasZeroInterestRate"] is bool hasZeroRate && hasZeroRate)
{
    <div class="alert alert-warning alert-dismissible fade show">
        <i class="fas fa-exclamation-triangle mr-2"></i>
        <strong>@Localizer["Data Issue Detected"]:</strong> @ViewData["ZeroInterestWarning"]
        <button type="button" class="close" data-dismiss="alert">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
}

@if (Model.SavingsAccount != null)
{
    <!-- Account Header -->
    <div class="account-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h2 class="mb-2">@Model.SavingsAccount.Plan?.Name</h2>
                <h3 class="mb-3">@Model.SavingsAccount.RzwAmount.ToString("N8", new System.Globalization.CultureInfo("en-US")) RZW</h3>
                <div class="d-flex align-items-center mb-3">
                    <span class="badge @Model.Summary.StatusBadgeClass mr-3">@Localizer[Model.Summary.StatusText]</span>
                    <span class="mr-3">@((Model.SavingsAccount.InterestRate * 100).ToString("N8").TrimEnd('0').TrimEnd('.'))% @Localizer["Daily Interest Rate"]</span>
                    <span>@Localizer["Started"]: @Model.SavingsAccount.StartDate.ToLocalTime().ToString("dd.MM.yyyy")</span>
                </div>

                <!-- Claim Interest Button for Matured Accounts -->
                @if (Model.Summary.IsMatured && Model.SavingsAccount.Status == RzwSavingsStatus.Active)
                {
                    <form method="post" asp-page-handler="ClaimInterest" asp-route-id="@Model.SavingsAccount.Id">
                        <div class="alert alert-success d-flex align-items-center mt-3">
                            <div class="mr-3">
                                <i class="fas fa-gift fa-2x"></i>
                            </div>
                            <div>
                                <strong>@Localizer["Maturity Reached! Claim your interest:"]</strong><br />
                                <span class="h5 text-success">@((Model.Summary.MaturityAmount - Model.SavingsAccount.RzwAmount).ToString("N8", new System.Globalization.CultureInfo("en-US"))) RZW</span>
                            </div>
                            <div class="ml-auto">
                                <button type="submit" class="btn btn-success btn-lg">
                                    <i class="fas fa-coins mr-2"></i>@Localizer["Claim Interest"]
                                </button>
                            </div>
                        </div>
                    </form>
                }

                <!-- Progress Bar -->
                @if (!Model.Summary.IsMatured)
                {
                    <div class="mb-2">
                        <div class="d-flex justify-content-between">
                            <span>@Localizer["Progress"]</span>
                            <span>@Localizer["Day"] @Model.Summary.ElapsedDays @Localizer["of"] @Model.Summary.TotalDays</span>
                        </div>
                        <div class="progress progress-custom">
                            <div class="progress-bar" role="progressbar" style="width: @(Model.Summary.ProgressPercentage.ToString("F1", System.Globalization.CultureInfo.InvariantCulture))%"></div>
                        </div>
                    </div>
                }
            </div>
            <div class="col-md-4 text-right">
                <div class="text-center">
                    <i class="fas fa-piggy-bank" style="font-size: 4rem; opacity: 0.7;"></i>
                    <div class="mt-2">
                        @if (Model.Summary.IsMatured)
                        {
                            <span class="badge badge-success badge-lg">@Localizer["Matured"]</span>
                        }
                        else
                        {
                            <div>@Model.Summary.RemainingDays @Localizer["Days Remaining"]</div>
                            <small>@Localizer["Maturity Date"]: @Model.SavingsAccount.MaturityDate.ToLocalTime().ToString("dd.MM.yyyy")</small>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Plan Details Section -->
    @if (Model.SavingsAccount.Plan != null)
    {
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="m-0">
                    <i class="fas fa-info-circle mr-2"></i>@Localizer["Plan Details"]
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">@Localizer["Plan Name"]:</dt>
                            <dd class="col-sm-7">@Model.SavingsAccount.Plan.Name</dd>

                            <dt class="col-sm-5">@Localizer["Term Type"]:</dt>
                            <dd class="col-sm-7">@Localizer[Model.SavingsAccount.Plan.TermType]</dd>

                            <dt class="col-sm-5">@Localizer["Term Duration"]:</dt>
                            <dd class="col-sm-7">@Model.SavingsAccount.Plan.TermDuration @Localizer["Days"]</dd>

                            <dt class="col-sm-5">@Localizer["Daily Interest Rate"]:</dt>
                            <dd class="col-sm-7">@((Model.SavingsAccount.Plan.InterestRate * 100).ToString("N8").TrimEnd('0').TrimEnd('.'))%</dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">@Localizer["Effective APY"]:</dt>
                            <dd class="col-sm-7">@(RzwSavingsCalculationHelper.GetAPYAsFlooredPercentage(Model.SavingsAccount.Plan.InterestRate).ToString("N2"))%</dd>

                            <dt class="col-sm-5">@Localizer["Min Amount"]:</dt>
                            <dd class="col-sm-7">@(RzwSavingsCalculationHelper.GetMinAmountDisplayText(Model.SavingsAccount.Plan.MinRzwAmount))</dd>

                            <dt class="col-sm-5">@Localizer["Max Amount"]:</dt>
                            <dd class="col-sm-7">@(RzwSavingsCalculationHelper.GetMaxAmountDisplayText(Model.SavingsAccount.Plan.MaxRzwAmount, Localizer["Unlimited"]))</dd>

                            <dt class="col-sm-5">@Localizer["Auto Renew"]:</dt>
                            <dd class="col-sm-7">
                                @if (Model.SavingsAccount.Status != "Active" || Model.Summary.IsMatured)
                                {
                                    <span class="font-weight-bold" style="min-width:90px;">@Localizer[Model.SavingsAccount.AutoRenew ? "Enabled" : "Disabled"]</span>
                                }
                                else
                                {
                                    <form method="post" asp-page-handler="UpdateAutoRenew" asp-route-id="@Model.SavingsAccount.Id" id="autoRenewForm" class="d-inline">
                                        <span id="autoRenewStatusLabel" class="font-weight-bold mr-3" style="min-width:90px;">@Localizer[Model.SavingsAccount.AutoRenew ? "Enabled" : "Disabled"]</span>
                                        @if (Model.SavingsAccount.AutoRenew)
                                        {
                                            <button type="submit" id="disableAutoRenewBtn" name="AutoRenewAction" value="disable" class="btn btn-danger btn-sm">@Localizer["Disable Auto Renew"]</button>
                                        }
                                        else
                                        {
                                            <button type="submit" id="enableAutoRenewBtn" name="AutoRenewAction" value="enable" class="btn btn-success btn-sm">@Localizer["Enable Auto Renew"]</button>
                                        }
                                    </form>
                                }
                                <script src="/plugins/sweetalert2/sweetalert2.all.js"></script>
                                <script>
                                    document.addEventListener('DOMContentLoaded', function () {
                                        var enableBtn = document.getElementById('enableAutoRenewBtn');
                                        var disableBtn = document.getElementById('disableAutoRenewBtn');
                                        var form = document.getElementById('autoRenewForm');
                                        var swalAreYouSure = @Html.Raw(System.Text.Json.JsonSerializer.Serialize(Localizer["Are you sure?"].Value));
                                        var swalChangeText = @Html.Raw(System.Text.Json.JsonSerializer.Serialize(Localizer["Are you sure you want to change the auto-renew setting?"].Value));
                                        var swalYes = @Html.Raw(System.Text.Json.JsonSerializer.Serialize(Localizer["Yes"].Value));
                                        var swalCancel = @Html.Raw(System.Text.Json.JsonSerializer.Serialize(Localizer["Cancel"].Value));
                                        var autoRenewConfirmed = false;
                                        if (enableBtn) {
                                            enableBtn.addEventListener('click', function (e) {
                                                if (!autoRenewConfirmed) {
                                                    e.preventDefault();
                                                    Swal.fire({
                                                        title: swalAreYouSure,
                                                        text: swalChangeText,
                                                        icon: 'question',
                                                        showCancelButton: true,
                                                        confirmButtonText: swalYes,
                                                        cancelButtonText: swalCancel,
                                                        reverseButtons: true
                                                    }).then((result) => {
                                                        if (result.value || result.isConfirmed) {
                                                            autoRenewConfirmed = true;
                                                            enableBtn.click();
                                                        }
                                                    });
                                                } else {
                                                    autoRenewConfirmed = false;
                                                }
                                            });
                                        }
                                        if (disableBtn) {
                                            disableBtn.addEventListener('click', function (e) {
                                                if (!autoRenewConfirmed) {
                                                    e.preventDefault();
                                                    Swal.fire({
                                                        title: swalAreYouSure,
                                                        text: swalChangeText,
                                                        icon: 'question',
                                                        showCancelButton: true,
                                                        confirmButtonText: swalYes,
                                                        cancelButtonText: swalCancel,
                                                        reverseButtons: true
                                                    }).then((result) => {
                                                        if (result.value || result.isConfirmed) {
                                                            autoRenewConfirmed = true;
                                                            disableBtn.click();
                                                        }
                                                    });
                                                } else {
                                                    autoRenewConfirmed = false;
                                                }
                                            });
                                        }
                                    });
                                </script>
                            </dd>
                        </dl>
                    </div>
                </div>

                @if (!string.IsNullOrEmpty(Model.SavingsAccount.Plan.Description))
                {
                    <div class="mt-3">
                        <strong>@Localizer["Description"]:</strong>
                        <p class="mt-2 mb-0">@Model.SavingsAccount.Plan.Description</p>
                    </div>
                }
            </div>
        </div>
    }

    <!-- Enhanced Statistics -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="stat-card">
                <div class="icon text-primary">
                    <i class="fas fa-coins"></i>
                </div>
                <div class="value text-primary">@Model.SavingsAccount.RzwAmount.ToString("N8", new System.Globalization.CultureInfo("en-US"))</div>
                <div class="label">@Localizer["Initial Amount"] (RZW)</div>
            </div>
        </div>

        <div class="col-md-3 mb-3">
            <div class="stat-card">
                <div class="icon text-success">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="value text-success">@Model.Summary.TotalInterestEarned.ToString("N8", new System.Globalization.CultureInfo("en-US"))</div>
                <div class="label">@Localizer["Total Interest Earned"] (RZW)</div>
            </div>
        </div>

        <div class="col-md-3 mb-3">
            <div class="stat-card">
                <div class="icon text-info">
                    <i class="fas fa-calendar-check"></i>
                </div>
                <div class="value text-info">@Model.Summary.MaturityAmount.ToString("N8", new System.Globalization.CultureInfo("en-US"))</div>
                <div class="label">@Localizer["Maturity Amount"] (RZW)</div>
            </div>
        </div>

        <div class="col-md-3 mb-3">
            <div class="stat-card">
                <div class="icon text-warning">
                    <i class="fas fa-percentage"></i>
                </div>
                <div class="value text-warning">@(RzwSavingsCalculationHelper.GetAPYAsFlooredPercentage(Model.SavingsAccount.InterestRate).ToString("N2"))%</div>
                <div class="label">@Localizer["Effective APY"]</div>
            </div>
        </div>
    </div>

    <!-- Additional Metrics -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="stat-card">
                <div class="icon text-info">
                    <i class="fas fa-calendar-day"></i>
                </div>
                <div class="value text-info">@(Model.SavingsAccount.Plan != null ? RzwSavingsCalculationHelper.CalculateDailyInterest(Model.SavingsAccount.RzwAmount, Model.SavingsAccount.Plan).ToString("N8", new System.Globalization.CultureInfo("en-US")) : "0.********")</div>
                <div class="label">@Localizer["Interest Rate"] (RZW)</div>
            </div>
        </div>

        <div class="col-md-3 mb-3">
            <div class="stat-card">
                <div class="icon text-primary">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="value text-primary">@Model.Summary.ElapsedDays</div>
                <div class="label">@Localizer["Days Held"]</div>
            </div>
        </div>

        <div class="col-md-3 mb-3">
            <div class="stat-card">
                <div class="icon text-secondary">
                    <i class="fas fa-balance-scale"></i>
                </div>
                <div class="value text-secondary">@((Model.SavingsAccount.RzwAmount + Model.Summary.TotalInterestEarned).ToString("N8", new System.Globalization.CultureInfo("en-US")))</div>
                <div class="label">@Localizer["Current Balance"] (RZW)</div>
            </div>
        </div>

        <div class="col-md-3 mb-3">
            <div class="stat-card">
                <div class="icon text-success">
                    <i class="fas fa-trending-up"></i>
                </div>
                <div class="value text-success">@(Model.Summary.TotalInterestEarned > 0 ? ((Model.Summary.TotalInterestEarned / Model.SavingsAccount.RzwAmount) * 100).ToString("N2") : "0.00")%</div>
                <div class="label">@Localizer["Return on Investment"]</div>
            </div>
        </div>
    </div>

    <!-- Interest Projections -->
    @if (!Model.Summary.IsMatured)
    {
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="m-0">
                    <i class="fas fa-calculator mr-2"></i>@Localizer["Interest Projections"]
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="projection-item">
                            <div class="projection-label">@Localizer["Remaining Interest"]</div>
                            <div class="projection-value text-success">
                                @{
                                    var remainingInterest = Model.Summary.MaturityAmount - Model.SavingsAccount.RzwAmount - Model.Summary.TotalInterestEarned;
                                }
                                @remainingInterest.ToString("N8", new System.Globalization.CultureInfo("en-US")) RZW
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="projection-item">
                            <div class="projection-label">@Localizer["Total Interest at Maturity"]</div>
                            <div class="projection-value text-info">
                                @{
                                    var totalProjectedInterest = Model.Summary.MaturityAmount - Model.SavingsAccount.RzwAmount;
                                }
                                @totalProjectedInterest.ToString("N8", new System.Globalization.CultureInfo("en-US")) RZW
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="projection-item">
                            <div class="projection-label">@Localizer["Average Daily Interest"]</div>
                            <div class="projection-value text-primary">
                                @(Model.SavingsAccount.Plan != null ? RzwSavingsCalculationHelper.CalculateDailyInterest(Model.SavingsAccount.RzwAmount, Model.SavingsAccount.Plan).ToString("N8", new System.Globalization.CultureInfo("en-US")) : "0.********") RZW
                            </div>
                        </div>
                    </div>
                </div>

                @if (Model.Summary.NextInterestPayment.HasValue)
                {
                    <div class="mt-3 p-3 bg-light rounded">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <strong>@Localizer["Next Interest Payment"]:</strong>
                                @Model.Summary.NextInterestPayment.Value.ToString("dd.MM.yyyy")
                            </div>
                            <div class="col-md-4 text-right">
                                <span class="badge badge-info">
                                    @Localizer["Tomorrow"]
                                </span>
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>
    }

    <!-- Early Withdrawal Warning -->
    @if (Model.Summary.CanWithdrawEarly && !Model.Summary.IsMatured)
    {
        <div class="early-withdraw-warning">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h5 class="mb-2">
                        <i class="fas fa-exclamation-triangle mr-2"></i>@Localizer["Early Withdrawal Available"]
                    </h5>
                    <p class="mb-0">
                        @Localizer["You can withdraw your RZW tokens early, but you may receive reduced interest based on the holding period."]
                    </p>
                    @if (Model.SavingsAccount.EarlyWithdrawalPenalty > 0)
                    {
                        <small class="text-muted">
                            @Localizer["Early Withdrawal Penalty"]: @((Model.SavingsAccount.EarlyWithdrawalPenalty * 100).ToString("N2"))%
                        </small>
                    }
                </div>
                <div class="col-md-4 text-right">
                    <form method="post" asp-page-handler="EarlyWithdraw" asp-route-id="@Model.SavingsAccount.Id" id="earlyWithdrawForm">
                        <button type="submit" class="btn btn-light btn-lg" id="earlyWithdrawBtn">
                            <i class="fas fa-hand-holding-usd mr-2"></i>@Localizer["Withdraw Early"]
                        </button>
                    </form>
                </div>
            </div>
        </div>
        <script src="/plugins/sweetalert2/sweetalert2.all.js"></script>
        <script>
            document.addEventListener('DOMContentLoaded', function () {
                var earlyWithdrawBtn = document.getElementById('earlyWithdrawBtn');
                var earlyWithdrawForm = document.getElementById('earlyWithdrawForm');
                var swalAreYouSure = @Html.Raw(System.Text.Json.JsonSerializer.Serialize(Localizer["Are you sure?"].Value));
                var swalChangeText = @Html.Raw(System.Text.Json.JsonSerializer.Serialize(Localizer["Are you sure you want to withdraw early? This action cannot be undone."].Value));
                var swalYes = @Html.Raw(System.Text.Json.JsonSerializer.Serialize(Localizer["Yes"].Value));
                var swalCancel = @Html.Raw(System.Text.Json.JsonSerializer.Serialize(Localizer["Cancel"].Value));
                var earlyWithdrawConfirmed = false;
                if (earlyWithdrawBtn) {
                    earlyWithdrawBtn.addEventListener('click', function (e) {
                        if (!earlyWithdrawConfirmed) {
                            e.preventDefault();
                            Swal.fire({
                                title: swalAreYouSure,
                                text: swalChangeText,
                                icon: 'warning',
                                showCancelButton: true,
                                confirmButtonText: swalYes,
                                cancelButtonText: swalCancel,
                                reverseButtons: true
                            }).then((result) => {
                                if (result.value || result.isConfirmed) {
                                    earlyWithdrawConfirmed = true;
                                    earlyWithdrawBtn.click();
                                }
                            });
                        } else {
                            earlyWithdrawConfirmed = false;
                        }
                    });
                }
            });
        </script>
    }

    <!-- Interest History -->
    <div class="card interest-table">
        <div class="card-header">
            <h5 class="m-0">
                <i class="fas fa-history mr-2"></i>@Localizer["Interest Payment History"] (@Model.InterestHistory.Count)
            </h5>
        </div>
        <div class="card-body">
            @if (Model.InterestHistory.Any())
            {
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>@Localizer["Date"]</th>
                                <th>@Localizer["Interest Amount"]</th>
                                <th>@Localizer["Daily Rate"]</th>
                                <th>@Localizer["Principal Amount"]</th>
                                <th>@Localizer["Accumulated Interest"]</th>
                                <th>@Localizer["Balance After Interest"]</th>
                                <th>@Localizer["Days Elapsed"]</th>
                                <th>@Localizer["Status"]</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var payment in Model.InterestHistory.OrderByDescending(h => h.PaymentDate))
                            {
                                <tr>
                                    <td>@payment.PaymentDate.ToLocalTime().ToString("dd.MM.yyyy HH:mm")</td>
                                    <td class="text-success">+@payment.RzwAmount.ToString("N8", new System.Globalization.CultureInfo("en-US")) RZW</td>
                                    <td>
                                        @if (payment.DailyRate.HasValue)
                                        {
                                            <span>@((payment.DailyRate.Value * 100).ToString("N8"))%</span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">-</span>
                                        }
                                    </td>
                                    <td>
                                        @if (payment.PrincipalAmount.HasValue)
                                        {
                                            <span>@payment.PrincipalAmount.Value.ToString("N8", new System.Globalization.CultureInfo("en-US")) RZW</span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">-</span>
                                        }
                                    </td>
                                    <td>
                                        @if (payment.AccumulatedInterest.HasValue)
                                        {
                                            <span class="text-info">@payment.AccumulatedInterest.Value.ToString("N8", new System.Globalization.CultureInfo("en-US")) RZW</span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">-</span>
                                        }
                                    </td>
                                    <td>
                                        @{
                                            var balanceAfterInterest = (payment.PrincipalAmount ?? Model.SavingsAccount.RzwAmount) + (payment.AccumulatedInterest ?? 0);
                                        }
                                        <strong>@balanceAfterInterest.ToString("N8", new System.Globalization.CultureInfo("en-US")) RZW</strong>
                                    </td>
                                    <td>@((payment.PaymentDate - Model.SavingsAccount.StartDate).Days)</td>
                                    <td>
                                        <span class="badge badge-success">@Localizer["Paid"]</span>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else
            {
                <div class="text-center text-muted py-4">
                    <i class="fas fa-clock fa-3x mb-3"></i>
                    <p>@Localizer["No interest payments yet. Interest will be paid daily starting from tomorrow."]</p>
                </div>
            }
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row mt-4">
        <div class="col-md-12 text-center">
            <a href="/MyAccount/RzwSavings" class="btn btn-secondary btn-lg mr-3">
                <i class="fas fa-arrow-left mr-2"></i>@Localizer["Back to My Savings"]
            </a>
            <a href="/MyAccount/RzwSavings/InterestHistory" class="btn btn-info btn-lg">
                <i class="fas fa-history mr-2"></i>@Localizer["View All Interest History"]
            </a>
        </div>
    </div>
}
else
{
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-triangle mr-2"></i>
        @Localizer["Savings account not found or you don't have permission to view it."]
    </div>
}



