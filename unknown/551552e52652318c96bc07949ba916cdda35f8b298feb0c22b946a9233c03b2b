using RazeWinComTr.Areas.Admin.Models;

namespace RazeWinComTr.Areas.Admin.Services.Interfaces;

/// <summary>
/// Email verification service interface
/// </summary>
public interface IEmailVerificationService
{
    /// <summary>
    /// Generates a new verification token for the user's email
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <returns>Generated token</returns>
    Task<string> GenerateVerificationTokenAsync(int userId);

    /// <summary>
    /// Sends verification email to user
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="email">Email address</param>
    /// <param name="userName">User's full name</param>
    /// <returns>True if email was sent successfully</returns>
    Task<bool> SendVerificationEmailAsync(int userId, string email, string userName);

    /// <summary>
    /// Verifies email token
    /// </summary>
    /// <param name="token">Verification token</param>
    /// <returns>True if verification was successful</returns>
    Task<bool> VerifyEmailTokenAsync(string token);

    /// <summary>
    /// Checks if user can send verification email (rate limiting)
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <returns>True if user can send email</returns>
    Task<bool> CanSendVerificationEmailAsync(int userId);

    /// <summary>
    /// Gets time until next email can be sent
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <returns>TimeSpan until next email</returns>
    Task<TimeSpan?> GetTimeUntilNextEmailAsync(int userId);

    /// <summary>
    /// Gets today's attempt count
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <returns>Number of attempts today</returns>
    Task<int> GetTodayAttemptsAsync(int userId);

    /// <summary>
    /// Checks if email is verified
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <returns>True if email is verified</returns>
    Task<bool> IsEmailVerifiedAsync(int userId);

    /// <summary>
    /// Gets email verification status
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <returns>Email verification status</returns>
    Task<EmailVerificationStatus> GetVerificationStatusAsync(int userId);
}


