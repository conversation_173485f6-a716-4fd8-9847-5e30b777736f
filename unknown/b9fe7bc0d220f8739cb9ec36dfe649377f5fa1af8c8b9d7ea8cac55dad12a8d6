namespace RazeWinComTr.Areas.Admin.ViewModels.RzwDistributionReports
{
    /// <summary>
    /// View model for referral reward distribution data
    /// </summary>
    public class ReferralRewardDistributionViewModel
    {
        /// <summary>
        /// Total RZW distributed through referral rewards
        /// </summary>
        public decimal TotalRzwDistributed { get; set; }

        /// <summary>
        /// Number of referral rewards distributed
        /// </summary>
        public int TotalRewardCount { get; set; }

        /// <summary>
        /// Number of unique users who received rewards
        /// </summary>
        public int UniqueUserCount { get; set; }

        /// <summary>
        /// Number of unique referred users who generated rewards
        /// </summary>
        public int UniqueReferredUserCount { get; set; }

        /// <summary>
        /// Average reward amount
        /// </summary>
        public decimal AverageRewardAmount => TotalRewardCount > 0 
            ? Math.Round(TotalRzwDistributed / TotalRewardCount, 8) 
            : 0;

        /// <summary>
        /// Daily breakdown of referral rewards
        /// </summary>
        public List<DailyReferralRewardViewModel> DailyBreakdown { get; set; } = new();

        /// <summary>
        /// Breakdown by referral level
        /// </summary>
        public List<ReferralLevelBreakdownViewModel> LevelBreakdown { get; set; } = new();

        /// <summary>
        /// Breakdown by package type
        /// </summary>
        public List<PackageBreakdownViewModel> PackageBreakdown { get; set; } = new();

        /// <summary>
        /// Breakdown by reward type (DEPOSIT, etc.)
        /// </summary>
        public List<RewardTypeBreakdownViewModel> RewardTypeBreakdown { get; set; } = new();
    }

    /// <summary>
    /// Daily referral reward data
    /// </summary>
    public class DailyReferralRewardViewModel
    {
        /// <summary>
        /// Date of the rewards
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// Total RZW distributed on this date
        /// </summary>
        public decimal TotalRzwDistributed { get; set; }

        /// <summary>
        /// Number of rewards distributed on this date
        /// </summary>
        public int RewardCount { get; set; }

        /// <summary>
        /// Number of unique users who received rewards
        /// </summary>
        public int UniqueUserCount { get; set; }
    }

    /// <summary>
    /// Referral level breakdown
    /// </summary>
    public class ReferralLevelBreakdownViewModel
    {
        /// <summary>
        /// Referral level (1, 2, 3, 4)
        /// </summary>
        public int Level { get; set; }

        /// <summary>
        /// Total RZW distributed for this level
        /// </summary>
        public decimal TotalRzwDistributed { get; set; }

        /// <summary>
        /// Number of rewards for this level
        /// </summary>
        public int RewardCount { get; set; }

        /// <summary>
        /// Percentage of total referral reward distribution
        /// </summary>
        public decimal Percentage { get; set; }
    }

    /// <summary>
    /// Package breakdown for referral rewards
    /// </summary>
    public class PackageBreakdownViewModel
    {
        /// <summary>
        /// Package name
        /// </summary>
        public string PackageName { get; set; } = string.Empty;

        /// <summary>
        /// Total RZW distributed for this package
        /// </summary>
        public decimal TotalRzwDistributed { get; set; }

        /// <summary>
        /// Number of rewards for this package
        /// </summary>
        public int RewardCount { get; set; }

        /// <summary>
        /// Percentage of total referral reward distribution
        /// </summary>
        public decimal Percentage { get; set; }
    }

    /// <summary>
    /// Reward type breakdown
    /// </summary>
    public class RewardTypeBreakdownViewModel
    {
        /// <summary>
        /// Reward type (DEPOSIT, etc.)
        /// </summary>
        public string RewardType { get; set; } = string.Empty;

        /// <summary>
        /// Total RZW distributed for this reward type
        /// </summary>
        public decimal TotalRzwDistributed { get; set; }

        /// <summary>
        /// Number of rewards for this reward type
        /// </summary>
        public int RewardCount { get; set; }

        /// <summary>
        /// Percentage of total referral reward distribution
        /// </summary>
        public decimal Percentage { get; set; }
    }
}
