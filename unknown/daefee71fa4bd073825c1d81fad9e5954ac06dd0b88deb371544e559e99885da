using Microsoft.Extensions.Localization;
using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Tests.TestInfrastructure.Base;

namespace RazeWinComTr.Tests.Unit.Services
{
    public class PackageRewardPercentageServiceTests : TestBase
    {
        private AppDbContext CreateIsolatedContext() => CreateDbContext(CreateUniqueDatabaseName("PackageRewardPercentageService"));

        private PackageRewardPercentageService CreateService(AppDbContext context)
        {
            var localizer = new Mock<IStringLocalizer<SharedResource>>();
            localizer.Setup(x => x[It.IsAny<string>(), It.IsAny<object[]>()])
                .Returns((string key, object[] args) => new LocalizedString(key, string.Format(key, args)));
            return new PackageRewardPercentageService(context, localizer.Object);
        }

        [Fact]
        public async Task GetByIdAsync_ReturnsCorrectEntity()
        {
            using var context = CreateIsolatedContext();
            var package = new Package { Id = 1, Name = "Test" };
            var entity = new PackageRewardPercentage { Id = 1, PackageId = 1, Level = 1, RzwPercentage = 5, TlPercentage = 10, Package = package };
            context.Packages.Add(package);
            context.PackageRewardPercentages.Add(entity);
            await context.SaveChangesAsync();
            var service = CreateService(context);
            var result = await service.GetByIdAsync(1);
            Assert.NotNull(result);
            Assert.Equal(1, result!.Id);
        }

        [Fact]
        public async Task GetByPackageAndLevelAsync_ReturnsCorrectEntity()
        {
            using var context = CreateIsolatedContext();
            var entity = new PackageRewardPercentage { Id = 2, PackageId = 2, Level = 2, RzwPercentage = 7, TlPercentage = 3 };
            context.PackageRewardPercentages.Add(entity);
            await context.SaveChangesAsync();
            var service = CreateService(context);
            var result = await service.GetByPackageAndLevelAsync(2, 2);
            Assert.NotNull(result);
            Assert.Equal(2, result!.Id);
        }

        [Fact]
        public async Task GetByPackageIdAsync_ReturnsAllForPackage()
        {
            using var context = CreateIsolatedContext();
            context.PackageRewardPercentages.AddRange(
                new PackageRewardPercentage { Id = 3, PackageId = 3, Level = 1, RzwPercentage = 1, TlPercentage = 2 },
                new PackageRewardPercentage { Id = 4, PackageId = 3, Level = 2, RzwPercentage = 2, TlPercentage = 3 }
            );
            await context.SaveChangesAsync();
            var service = CreateService(context);
            var result = await service.GetByPackageIdAsync(3);
            Assert.Equal(2, result.Count);
        }

        [Fact]
        public async Task GetListAsync_ReturnsViewModels()
        {
            using var context = CreateIsolatedContext();
            var package = new Package { Id = 4, Name = "Pack4" };
            context.Packages.Add(package);
            context.PackageRewardPercentages.Add(new PackageRewardPercentage { Id = 5, PackageId = 4, Level = 1, RzwPercentage = 1, TlPercentage = 2, Package = package });
            await context.SaveChangesAsync();
            var service = CreateService(context);
            var result = await service.GetListAsync();
            Assert.Single(result);
            Assert.Equal("Pack4", result[0].PackageName);
        }

        [Fact]
        public async Task GetPercentageAsync_ReturnsRzwPercentage()
        {
            using var context = CreateIsolatedContext();
            context.PackageRewardPercentages.Add(new PackageRewardPercentage { Id = 6, PackageId = 5, Level = 1, RzwPercentage = 9, TlPercentage = 1 });
            await context.SaveChangesAsync();
            var service = CreateService(context);
            var result = await service.GetPercentageAsync(5, 1);
            Assert.Equal(9, result);
        }

        [Fact]
        public async Task ValidatePercentageHierarchyAsync_ValidatesCorrectly()
        {
            using var context = CreateIsolatedContext();
            context.PackageRewardPercentages.Add(new PackageRewardPercentage { Id = 7, PackageId = 6, Level = 1, RzwPercentage = 10, TlPercentage = 1 });
            await context.SaveChangesAsync();
            var service = CreateService(context);
            var (isValid, error) = await service.ValidatePercentageHierarchyAsync(6, 2, 5);
            Assert.True(isValid);
            (isValid, error) = await service.ValidatePercentageHierarchyAsync(6, 2, 15);
            Assert.False(isValid);
            Assert.Contains("cannot be greater", error);
        }

        [Fact]
        public async Task CreateAsync_CreatesOrUpdatesEntity()
        {
            using var context = CreateIsolatedContext();
            var service = CreateService(context);
            var entity = new PackageRewardPercentage { PackageId = 7, Level = 1, RzwPercentage = 5, TlPercentage = 2 };
            var created = await service.CreateAsync(entity);
            Assert.NotNull(created);
            Assert.Equal(5, created.RzwPercentage);
            // Try to create again with same package/level, should update
            entity.RzwPercentage = 10;
            var updated = await service.CreateAsync(entity);
            Assert.Equal(10, updated.RzwPercentage);
        }

        [Fact]
        public async Task CreateAsync_ThrowsOnInvalidHierarchy()
        {
            using var context = CreateIsolatedContext();
            var service = CreateService(context);
            context.PackageRewardPercentages.Add(new PackageRewardPercentage { PackageId = 8, Level = 1, RzwPercentage = 10, TlPercentage = 1 });
            await context.SaveChangesAsync();
            var entity = new PackageRewardPercentage { PackageId = 8, Level = 2, RzwPercentage = 15, TlPercentage = 2 };
            await Assert.ThrowsAsync<InvalidOperationException>(() => service.CreateAsync(entity));
        }

        [Fact]
        public async Task UpdateAsync_UpdatesEntity()
        {
            using var context = CreateIsolatedContext();
            var entity = new PackageRewardPercentage { Id = 9, PackageId = 9, Level = 1, RzwPercentage = 5, TlPercentage = 2 };
            context.PackageRewardPercentages.Add(entity);
            await context.SaveChangesAsync();
            var service = CreateService(context);
            entity.RzwPercentage = 7;
            var updated = await service.UpdateAsync(entity);
            Assert.Equal(7, updated.RzwPercentage);
        }

        [Fact]
        public async Task UpdateAsync_ThrowsOnInvalidHierarchy()
        {
            using var context = CreateIsolatedContext();
            var entity1 = new PackageRewardPercentage { Id = 10, PackageId = 10, Level = 1, RzwPercentage = 10, TlPercentage = 1 };
            var entity2 = new PackageRewardPercentage { Id = 11, PackageId = 10, Level = 2, RzwPercentage = 5, TlPercentage = 2 };
            context.PackageRewardPercentages.AddRange(entity1, entity2);
            await context.SaveChangesAsync();
            var service = CreateService(context);
            entity2.RzwPercentage = 15;
            await Assert.ThrowsAsync<InvalidOperationException>(() => service.UpdateAsync(entity2));
        }

        [Fact]
        public async Task DeleteAsync_RemovesEntity()
        {
            using var context = CreateIsolatedContext();
            var entity = new PackageRewardPercentage { Id = 12, PackageId = 11, Level = 1, RzwPercentage = 5, TlPercentage = 2 };
            context.PackageRewardPercentages.Add(entity);
            await context.SaveChangesAsync();
            var service = CreateService(context);
            await service.DeleteAsync(12);
            var found = await context.PackageRewardPercentages.FindAsync(12);
            Assert.Null(found);
        }
    }
}
