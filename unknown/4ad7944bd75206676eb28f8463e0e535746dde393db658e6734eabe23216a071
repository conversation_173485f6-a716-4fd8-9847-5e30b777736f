using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using RazeWinComTr.Areas.Admin.Enums;

namespace RazeWinComTr.Areas.Admin.DbModel;

[Table("USER_VERIFICATION")]
public partial class UserVerification
{
    [Key]
    [Column("VERIFICATION_ID")]
    public int VerificationId { get; set; }

    [Column("USER_ID")]
    public int UserId { get; set; }

    [Column("VERIFICATION_TYPE")]
    public VerificationType VerificationType { get; set; }

    [Column("TARGET_VALUE")]
    [StringLength(255)]
    public string TargetValue { get; set; } = string.Empty; // Email address, phone number, etc.

    [Column("VERIFICATION_TOKEN")]
    [StringLength(255)]
    public string? VerificationToken { get; set; }

    [Column("TOKEN_EXPIRY", TypeName = "datetime")]
    public DateTime? TokenExpiry { get; set; }

    [Column("ATTEMPTS_COUNT")]
    public int AttemptsCount { get; set; } = 0;

    [Column("LAST_ATTEMPT", TypeName = "datetime")]
    public DateTime? LastAttempt { get; set; }

    [Column("IS_VERIFIED")]
    public bool IsVerified { get; set; } = false;

    [Column("VERIFIED_DATE", TypeName = "datetime")]
    public DateTime? VerifiedDate { get; set; }

    [Column("CR_DATE", TypeName = "datetime")]
    public DateTime CrDate { get; set; } = DateTime.UtcNow;

    [Column("MOD_DATE", TypeName = "datetime")]
    public DateTime? ModDate { get; set; }

    [Column("IS_ACTIVE")]
    public int IsActive { get; set; } = 1;

    // Navigation properties
    [ForeignKey("UserId")]
    public virtual User User { get; set; } = null!;
}
