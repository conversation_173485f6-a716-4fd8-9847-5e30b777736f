@page
@model RazeWinComTr.Areas.MyAccount.Pages.RzwSavings.IndexModel
@using Microsoft.Extensions.Localization
@using RazeWinComTr.Areas.Admin.Helpers
@inject IStringLocalizer<SharedResource> Localizer
@{
    ViewData["Title"] = Localizer["My Savings"];
}

@section Styles {
    <style>
        /* Enhanced Page Header Styling */
        .savings-page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .savings-page-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            pointer-events: none;
        }

        .savings-page-header .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 1;
        }

        .savings-page-header .page-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 0;
            position: relative;
            z-index: 1;
        }

        .savings-page-header .header-icon {
            font-size: 3rem;
            margin-right: 15px;
            opacity: 0.8;
            vertical-align: middle;
        }

        /* Responsive adjustments for header */
        @@media (max-width: 768px) {
            .savings-page-header {
                padding: 20px 15px;
                text-align: center;
            }

            .savings-page-header .page-title {
                font-size: 2rem;
            }

            .savings-page-header .header-icon {
                display: block;
                margin: 0 auto 15px;
            }
        }

        .rzw-savings-card {
            border-left: 4px solid #667eea;
            transition: all 0.3s ease;
        }
        
        .rzw-savings-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
        }
        
        .dashboard-stat {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            height: 100%;
        }
        
        .dashboard-stat .icon {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .dashboard-stat .value {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .dashboard-stat .label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .savings-account-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .savings-account-card:hover {
            border-color: #667eea;
            box-shadow: 0 2px 10px rgba(102, 126, 234, 0.1);
        }
        
        .plan-card {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .plan-card:hover {
            border-color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 5px 20px rgba(102, 126, 234, 0.15);
        }
        
        .progress-custom {
            height: 12px;
            border-radius: 6px;
            background-color: #e9ecef;
            overflow: hidden;
            margin: 0;
            position: relative !important;
            width: 100%;
        }

        .progress-custom .progress-bar {
            border-radius: 6px;
            background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
            transition: width 0.6s ease;
            position: relative !important;
            height: 100%;
        }
    </style>
}

<!-- Enhanced Page Header -->
<div class="savings-page-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="d-flex align-items-center">
                    <i class="fas fa-piggy-bank header-icon"></i>
                    <div>
                        <h1 class="page-title mb-0">@Localizer["My Savings"]</h1>
                        <p class="page-subtitle">@Localizer["Start earning interest on your RZW tokens with our savings plans."]</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 text-right">
                <ol class="breadcrumb bg-transparent mb-0" style="background: rgba(255,255,255,0.1) !important; border-radius: 8px; padding: 8px 15px;">
                    <li class="breadcrumb-item"><a href="/MyAccount/Dashboard" class="text-white-50">@Localizer["Home"]</a></li>
                    <li class="breadcrumb-item"><a href="/MyAccount/Wallet" class="text-white-50">@Localizer["My Wallet"]</a></li>
                    <li class="breadcrumb-item active text-white">@Localizer["My Savings"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Welcome Message -->
<div class="alert alert-info">
    <i class="fas fa-info-circle mr-2"></i>
    @Model.WelcomeMessage
</div>

<!-- Dashboard Statistics -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="dashboard-stat">
            <div class="icon text-warning">
                <i class="fas fa-list"></i>
            </div>
            <div class="value text-warning">@Model.Dashboard.ActiveAccountsCount</div>
            <div class="label">@Localizer["Active Savings Accounts"]</div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="dashboard-stat">
            <div class="icon text-primary">
                <i class="fas fa-piggy-bank"></i>
            </div>
            <div class="value text-primary">@NumberFormatHelper.FormatDecimalWithThousandSeparatorInvariant(Model.Dashboard.TotalInvestment, 8)</div>
            <div class="label">@Localizer["Total Investment"] (RZW)</div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="dashboard-stat">
            <div class="icon text-success">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="value text-success">@NumberFormatHelper.FormatDecimalWithThousandSeparatorInvariant(Model.Dashboard.TotalInterestEarned, 8)</div>
            <div class="label">@Localizer["Total Interest Earned"] (RZW)</div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="dashboard-stat">
            <div class="icon text-info">
                <i class="fas fa-calendar-alt"></i>
            </div>
            <div class="value text-info">@NumberFormatHelper.FormatDecimalWithThousandSeparatorInvariant(Model.Dashboard.ProjectedMonthlyEarnings, 8)</div>
            <div class="label">@Localizer["Projected Monthly Earnings"] (RZW)</div>
        </div>
    </div>
</div>

<!-- Current Balance -->
@if (Model.UserRzwBalance != null)
{
    <div class="card rzw-savings-card mb-4">
        <div class="card-header">
            <h5 class="m-0">
                <i class="fas fa-wallet mr-2"></i>@Localizer["Available Balance"]
            </h5>
        </div>
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h3 class="text-success mb-0">@NumberFormatHelper.FormatDecimalWithThousandSeparatorInvariant(Model.UserRzwBalance.AvailableRzw, 8) RZW</h3>
                    <small class="text-muted">@Localizer["Available for new savings accounts"]</small>
                </div>
                <div class="col-md-6 text-right">
                    @if (Model.HasAvailableBalance)
                    {
                        <a href="/MyAccount/RzwSavings/Create" class="btn btn-success btn-lg">
                            <i class="fas fa-plus mr-2"></i>@Localizer["Create New Savings Account"]
                        </a>
                    }
                    else
                    {
                        <div class="text-muted">
                            <i class="fas fa-info-circle mr-2"></i>@Localizer["No available balance for new savings"]
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
}

<!-- Active Savings Accounts -->
@if (Model.HasActiveAccounts)
{
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="m-0">
                <i class="fas fa-list mr-2"></i>@Localizer["Active Savings Accounts"] (@Model.ActiveAccounts.Count)
            </h5>
            <div class="card-tools">
                <a href="/MyAccount/RzwSavings/History" class="btn btn-tool">
                    <i class="fas fa-history"></i> @Localizer["View All Accounts"]
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                @foreach (var account in Model.ActiveAccounts)
                {

                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="savings-account-card p-3">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="mb-0">@account.PlanName</h6>
                                @if (!account.IsMatured)
                                {
                                    <span class="badge badge-success">@Localizer["Active"]</span>
                                }
                                else
                                {
                                    <span class="badge badge-info">@Localizer["Matured"]</span>
                                }
                            </div>
                            @if (!account.IsMatured)
                            {
                                <div class="mb-2">
                                    <small class="text-muted">@Localizer["Days Remaining"]: </small>
                                    <strong>@account.DaysRemaining</strong>
                                </div>

                                <div class="mb-2">
                                    <div class="d-flex justify-content-between align-items-center mb-1">
                                        <small class="text-muted">@Localizer["Progress"]</small>
                                        <small class="text-muted">@(account.ProgressPercentage.ToString("F1", System.Globalization.CultureInfo.CurrentCulture))%</small>
                                    </div>
                                    <div class="progress progress-custom">
                                        <div class="progress-bar" role="progressbar"
                                             style="width: @(account.ProgressPercentage.ToString("F1", System.Globalization.CultureInfo.InvariantCulture))%"
                                             aria-valuenow="@(account.ProgressPercentage.ToString("F1", System.Globalization.CultureInfo.InvariantCulture))"
                                             aria-valuemin="0"
                                             aria-valuemax="100">
                                        </div>
                                    </div>
                                </div>
                            }
                             <div class="mb-2">
                                <small class="text-muted">@Localizer["Amount"]: </small>
                                <strong>@NumberFormatHelper.FormatDecimalWithThousandSeparatorInvariant(account.RzwAmount, 8) RZW</strong>
                            </div>
                            
                            <div class="mb-2">
                                <small class="text-muted">@Localizer["Term Type"]: </small>
                                <strong>@Localizer[@account.TermType]</strong>
                            </div>

                            <div class="mb-2">
                                <small class="text-muted">@Localizer["Term Duration"]: </small>
                                <strong>@account.TermDisplayText</strong>
                            </div>

                            <div class="mb-2">
                                <small class="text-muted">@Localizer["Interest Rate"]: </small>
                                <strong>@account.InterestRate)</strong>
                            </div>

                            <div class="mb-2">
                                <small class="text-muted">@Localizer["Annual Percentage Yield"]: </small>
                                <strong>@(account.Plan != null ? (RzwSavingsCalculationHelper.CalculateAPY(account.InterestRate) * 100) : 0)%</strong>
                            </div>

                            <div class="mb-2">
                                <small class="text-muted">@Localizer["Start Date"]: </small>
                                <strong>@account.StartDate.ToLocalTime().ToString("dd.MM.yyyy")</strong>
                            </div>

                            <div class="mb-2">
                                <small class="text-muted">@Localizer["Maturity Date"]: </small>
                                <strong>@account.MaturityDate.ToLocalTime().ToString("dd.MM.yyyy")</strong>
                            </div>

                            @if (account.TotalEarnedRzw > 0)
                            {
                                <div class="mb-2">
                                    <small class="text-muted">@Localizer["Total Earned"]: </small>
                                    <strong class="text-success">@NumberFormatHelper.FormatDecimalWithThousandSeparatorInvariant(account.TotalEarnedRzw, 8) RZW</strong>
                                </div>
                            }

                            <div class="mb-2">
                                <small class="text-muted">@Localizer["Current Earned Interest"]: </small>
                                <strong class="text-info">@NumberFormatHelper.FormatDecimalWithThousandSeparatorInvariant(account.CurrentEarnedInterest, 8) RZW</strong>
                            </div>

                            <div class="mb-2">
                                <small class="text-muted">@Localizer["Projected Total Interest"]: </small>
                                <strong class="text-warning">@NumberFormatHelper.FormatDecimalWithThousandSeparatorInvariant(account.ProjectedTotalInterest, 8) RZW</strong>
                            </div>

                            <div class="mb-2">
                                <small class="text-muted">@Localizer["Projected Maturity Amount"]: </small>
                                <strong class="text-primary">@NumberFormatHelper.FormatDecimalWithThousandSeparatorInvariant(account.MaturityAmount, 8) RZW</strong>
                            </div>

                            <div class="mb-2">
                                <small class="text-muted">@Localizer["Early Withdrawal Penalty"]: </small>
                                <strong class="text-warning">@account.EarlyWithdrawalPenaltyDisplayText</strong>
                            </div>

                            <div class="mb-2">
                                <small class="text-muted">@Localizer["Auto Renewal"]: </small>
                                <strong class="@(account.AutoRenew ? "text-success" : "text-muted")">
                                    @Localizer[account.AutoRenewDisplayText]
                                </strong>
                            </div>



                            @if (!string.IsNullOrEmpty(account.PlanDescription))
                            {
                                <div class="mb-2">
                                    <small class="text-muted">@Localizer["Description"]: </small>
                                    <small class="text-info">@account.PlanDescription</small>
                                </div>
                            }

                            <div class="text-center mt-3">
                                <a href="/MyAccount/RzwSavings/Details?id=@account.Id" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye mr-1"></i>@Localizer["Details"]
                                </a>
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>
}

<!-- Toggle for Inactive Accounts -->
@{
    // Always show the toggle section, but conditionally show content
}
<div class="card mb-4">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="m-0">
                <i class="fas fa-archive mr-2"></i>@Localizer["Inactive Savings Accounts"]
                @if (Model.HasInactiveAccounts)
                {
                    <span class="badge badge-secondary ml-2">@Model.InactiveAccounts.Count</span>
                }
            </h5>
            <div>
                @if (!Model.ShowInactive)
                {
                    <a href="?ShowInactive=true" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-eye mr-1"></i>@Localizer["Show Inactive Accounts"]
                    </a>
                }
                else
                {
                    <a href="?" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye-slash mr-1"></i>@Localizer["Hide Inactive Accounts"]
                    </a>
                }
            </div>
        </div>
    </div>

        @if (Model.ShowInactive && Model.HasInactiveAccounts)
        {
            <div class="card-body">
                <div class="row">
                    @foreach (var account in Model.InactiveAccounts)
                    {
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="savings-account-card p-3 @(account.Status == RzwSavingsStatus.Matured ? "border-success" : account.Status == RzwSavingsStatus.Withdrawn ? "border-info" : "border-secondary")">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="mb-0">@account.PlanName</h6>
                                    <span class="badge @GetStatusBadgeClass(account.Status)">@Localizer[account.Status]</span>
                                </div>

                                <div class="mb-2">
                                    <h5 class="text-primary mb-1">@NumberFormatHelper.FormatDecimalWithThousandSeparatorInvariant(account.RzwAmount, 8) RZW</h5>
                                    <small class="text-muted">@Localizer["Principal Amount"]</small>
                                </div>

                                <div class="mb-2">
                                    <small class="text-muted">@Localizer["Started"]: </small>
                                    <strong>@account.StartDate.ToLocalTime().ToString("dd.MM.yyyy")</strong>
                                </div>

                                <div class="mb-2">
                                    <small class="text-muted">@Localizer["Maturity Date"]: </small>
                                    <strong>@account.MaturityDate.ToLocalTime().ToString("dd.MM.yyyy")</strong>
                                </div>

                                @if (account.TotalEarnedRzw > 0)
                                {
                                    <div class="mb-2">
                                        <small class="text-muted">@Localizer["Total Earned"]: </small>
                                        <strong class="text-success">@NumberFormatHelper.FormatDecimalWithThousandSeparatorInvariant(account.TotalEarnedRzw, 8) RZW</strong>
                                    </div>
                                }

                                <div class="text-center mt-3">
                                    <a href="/MyAccount/RzwSavings/Details?id=@account.Id" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye mr-1"></i>@Localizer["Details"]
                                    </a>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        }
        else if (Model.ShowInactive && !Model.HasInactiveAccounts)
        {
            <div class="card-body text-center text-muted py-4">
                <i class="fas fa-archive fa-3x mb-3"></i>
                <p>@Localizer["No inactive savings accounts found."]</p>
            </div>
        }
    </div>
</div>

@functions {
    private string GetStatusBadgeClass(string status)
    {
        return status switch
        {
            RzwSavingsStatus.Matured => "badge-success",
            RzwSavingsStatus.Withdrawn => "badge-info",
            RzwSavingsStatus.Cancelled => "badge-danger",
            _ => "badge-secondary"
        };
    }
}
