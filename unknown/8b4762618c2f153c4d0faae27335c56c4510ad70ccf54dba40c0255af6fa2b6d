namespace RazeWinComTr.Areas.Admin.ViewModels.RzwDistributionReports
{
    /// <summary>
    /// Base class for distribution summary view models
    /// </summary>
    public abstract class DistributionSummaryBaseViewModel
    {
        /// <summary>
        /// Total RZW distributed from savings interest
        /// </summary>
        public decimal SavingsInterestRzw { get; set; }

        /// <summary>
        /// Total RZW distributed from referral rewards
        /// </summary>
        public decimal ReferralRewardRzw { get; set; }

        /// <summary>
        /// Total RZW distributed from all sources
        /// </summary>
        public decimal TotalRzwDistributed => SavingsInterestRzw + ReferralRewardRzw;

        /// <summary>
        /// Number of savings interest payments
        /// </summary>
        public int SavingsInterestPaymentCount { get; set; }

        /// <summary>
        /// Number of referral reward payments
        /// </summary>
        public int ReferralRewardPaymentCount { get; set; }

        /// <summary>
        /// Total number of payments
        /// </summary>
        public int TotalPaymentCount => SavingsInterestPaymentCount + ReferralRewardPaymentCount;
    }

    /// <summary>
    /// Daily distribution summary
    /// </summary>
    public class DailyDistributionSummaryViewModel : DistributionSummaryBaseViewModel
    {
        /// <summary>
        /// Date of the distribution
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// Day of week for display purposes
        /// </summary>
        public string DayOfWeek => Date.ToString("dddd");

        /// <summary>
        /// Formatted date string
        /// </summary>
        public string FormattedDate => Date.ToString("yyyy-MM-dd");
    }

    /// <summary>
    /// Weekly distribution summary
    /// </summary>
    public class WeeklyDistributionSummaryViewModel : DistributionSummaryBaseViewModel
    {
        /// <summary>
        /// Year of the week
        /// </summary>
        public int Year { get; set; }

        /// <summary>
        /// Week number in the year
        /// </summary>
        public int WeekNumber { get; set; }

        /// <summary>
        /// Start date of the week
        /// </summary>
        public DateTime WeekStartDate { get; set; }

        /// <summary>
        /// End date of the week
        /// </summary>
        public DateTime WeekEndDate { get; set; }

        /// <summary>
        /// Formatted week range string
        /// </summary>
        public string FormattedWeekRange => $"{WeekStartDate:yyyy-MM-dd} - {WeekEndDate:yyyy-MM-dd}";

        /// <summary>
        /// Week identifier for display
        /// </summary>
        public string WeekIdentifier => $"{Year}-W{WeekNumber:D2}";
    }

    /// <summary>
    /// Monthly distribution summary
    /// </summary>
    public class MonthlyDistributionSummaryViewModel : DistributionSummaryBaseViewModel
    {
        /// <summary>
        /// Year of the month
        /// </summary>
        public int Year { get; set; }

        /// <summary>
        /// Month number (1-12)
        /// </summary>
        public int Month { get; set; }

        /// <summary>
        /// Month name
        /// </summary>
        public string MonthName => new DateTime(Year, Month, 1).ToString("MMMM");

        /// <summary>
        /// Formatted month string
        /// </summary>
        public string FormattedMonth => $"{Year}-{Month:D2}";

        /// <summary>
        /// Month identifier for display
        /// </summary>
        public string MonthIdentifier => $"{MonthName} {Year}";
    }

    /// <summary>
    /// Yearly distribution summary
    /// </summary>
    public class YearlyDistributionSummaryViewModel : DistributionSummaryBaseViewModel
    {
        /// <summary>
        /// Year of the distribution
        /// </summary>
        public int Year { get; set; }

        /// <summary>
        /// Formatted year string
        /// </summary>
        public string FormattedYear => Year.ToString();
    }
}
