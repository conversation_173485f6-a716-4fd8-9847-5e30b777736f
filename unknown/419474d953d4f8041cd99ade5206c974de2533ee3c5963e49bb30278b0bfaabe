using RazeWinComTr.Areas.Admin.Enums;
using RazeWinComTr.Areas.Admin.Models;

namespace RazeWinComTr.Areas.Admin.Services.Interfaces;

/// <summary>
/// Generic verification service interface for all types of user verifications
/// </summary>
public interface IVerificationService
{
    /// <summary>
    /// Generates a new verification token for the user
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="verificationType">Type of verification</param>
    /// <param name="targetValue">Target value to verify (email, phone, etc.)</param>
    /// <returns>Generated token</returns>
    Task<string> GenerateVerificationTokenAsync(int userId, VerificationType verificationType, string targetValue);

    /// <summary>
    /// Verifies the verification token
    /// </summary>
    /// <param name="token">Verification token</param>
    /// <returns>True if token is valid and verification was successful, false otherwise</returns>
    Task<bool> VerifyTokenAsync(string token);

    /// <summary>
    /// Checks if a token has already been verified
    /// </summary>
    /// <param name="token">Verification token</param>
    /// <returns>True if token is already verified</returns>
    Task<bool> IsTokenAlreadyVerifiedAsync(string token);

    /// <summary>
    /// Checks if user can send verification (rate limiting)
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="verificationType">Type of verification</param>
    /// <returns>True if user can send verification, false if rate limited</returns>
    Task<bool> CanSendVerificationAsync(int userId, VerificationType verificationType);

    /// <summary>
    /// Gets the remaining time until user can send another verification
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="verificationType">Type of verification</param>
    /// <returns>TimeSpan until next verification can be sent, null if can send now</returns>
    Task<TimeSpan?> GetTimeUntilNextVerificationAsync(int userId, VerificationType verificationType);

    /// <summary>
    /// Gets the number of verification attempts made today
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="verificationType">Type of verification</param>
    /// <returns>Number of attempts today</returns>
    Task<int> GetTodayAttemptsAsync(int userId, VerificationType verificationType);

    /// <summary>
    /// Checks if user's verification is already completed
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="verificationType">Type of verification</param>
    /// <returns>True if verification is completed, false otherwise</returns>
    Task<bool> IsVerifiedAsync(int userId, VerificationType verificationType);

    /// <summary>
    /// Gets user's verification status and related information
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="verificationType">Type of verification</param>
    /// <returns>Verification status information</returns>
    Task<VerificationStatus> GetVerificationStatusAsync(int userId, VerificationType verificationType);
}
