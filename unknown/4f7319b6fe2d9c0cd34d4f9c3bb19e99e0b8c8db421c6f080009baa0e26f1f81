using RazeWinComTr.Areas.Admin.DbModel;

namespace RazeWinComTr.Areas.Admin.Helpers;

/// <summary>
/// Interface for HTTP context helper operations
/// </summary>
public interface IHttpContextHelper
{
    /// <summary>
    /// Gets the base URL from the current HTTP request
    /// </summary>
    /// <returns>Base URL string</returns>
    string GetBaseUrl();

    /// <summary>
    /// Starts a user session with the specified authentication scheme using a User object
    /// </summary>
    /// <param name="user">The user object containing all user information</param>
    /// <param name="returnUrl">The URL to redirect to after authentication</param>
    /// <param name="authenticationSchemeName">The authentication scheme to use</param>
    /// <returns>A task representing the asynchronous operation</returns>
    Task StartUserSession(User user, string? returnUrl, string authenticationSchemeName);

    /// <summary>
    /// Sets a string value in the session cache
    /// </summary>
    /// <param name="key">Cache key</param>
    /// <param name="value">Value to cache</param>
    void CacheSetString(string key, string value);

    /// <summary>
    /// Gets a string value from the session cache
    /// </summary>
    /// <param name="key">Cache key</param>
    /// <param name="value">Default value if key not found</param>
    /// <returns>Cached value or default</returns>
    string? CacheGetString(string key, string value);
}
