using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Formats.Jpeg;
using SixLabors.ImageSharp.Processing;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services;

namespace RazeWinComTr.Areas.Admin.Pages.Download;

[AllowAnonymous]
public class IndexAnonymousByFileId : PageModel
{
    private readonly AppDbContext _context;

    private readonly FileService _fileService;

    public IndexAnonymousByFileId(AppDbContext context, FileService fileService)
    {
        _context = context;
        _fileService = fileService;
    }

    public async Task<IActionResult> OnGetAsync(string fileType, string id, string? size) //todo AUTH
    {
        if (string.IsNullOrEmpty(fileType)) return NotFound("fileType parameter is null");
        if (string.IsNullOrEmpty(id)) return NotFound("id parameter is null");
        var parsedIdStr = int.TryParse(id, out var genericId);
        if (!parsedIdStr) return BadRequest("Parse Error");
        string? fileRelativePath = null;

        if (fileType == nameof(DbModel.Market))
        {
            var entity = await _context.Markets
                .Where(p => p.IsActive == 1 && p.Id == genericId)
                .Select(p => new { p.IconUrl })
                .FirstOrDefaultAsync();
            if (entity == null) return NotFound("db record not found");

            fileRelativePath = entity.IconUrl;
        }

        if (fileRelativePath == null) return NotFound("fileRelativePath is null");
        if (string.IsNullOrEmpty(_fileService.FileStoragePath)) return NotFound("_fileService.FileStoragePath is null"); // Return 404

        // Resolve the full file path by combining the storage path with the relative filename
        var filePath = Path.Combine(_fileService.FileStoragePath, fileRelativePath);

        // Normalize the file path to prevent directory traversal
        var fullFilePath = Path.Combine(_fileService.FileStoragePath, filePath);
        var fileName = Path.GetFileName(fullFilePath); // Ensure only the file name is returned


        // Check if the file exists
        if (!System.IO.File.Exists(fullFilePath))
        {
            if (fileType == nameof(DbModel.Market))
            {
                //return noimg_coin.png
                return File("/site/images/noimg_coin.png", "image/png", "noimg_coin.png");
            }
            return NotFound($"fullFilePath file is not exists. ({fullFilePath})");
        }
        // Return the file content with the appropriate MIME type
        var fileBytes = await System.IO.File.ReadAllBytesAsync(fullFilePath);
        if (size != null)
        {
            var sizeArr = size.Split('x');
            if (sizeArr.Length != 2) return BadRequest("Invalid size parameter");
            var width = int.Parse(sizeArr[0]);
            var height = int.Parse(sizeArr[1]);
            using (var image = Image.Load(fileBytes)) // Load from byte array
            {
                // Resize the image to a thumbnail (200x200 for example)
                var thumbnail = image.Clone(ctx => ctx.Resize(width, height));

                // Compress the thumbnail to reduce file size
                using (var thumbnailStream = new MemoryStream())
                {
                    // Save the thumbnail image to the memory stream
                    await thumbnail.SaveAsync(thumbnailStream,
                        new JpegEncoder { Quality = 50 }); // 50% quality for smaller file size

                    // Ensure the stream's position is set to the beginning before using it
                    thumbnailStream.Position = 0;
                    // Return the thumbnail with cache headers
                    Response.Headers.CacheControl = "public, max-age=86400"; // Cache for 30 days
                    Response.Headers.Expires = DateTime.UtcNow.AddDays(1).ToString("R");
                    var contentType = ContentTypesHelper.GetImageContentType(fileName);
                    // Return the thumbnail as a JPEG image
                    return File(thumbnailStream.ToArray(), contentType, fileName);
                }
            }
        }

        return File(fileBytes, "application/octet-stream", fileName);
    }
}