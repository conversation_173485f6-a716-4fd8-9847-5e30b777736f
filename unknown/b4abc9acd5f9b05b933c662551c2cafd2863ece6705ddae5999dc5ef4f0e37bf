using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using RazeWinComTr.Areas.Admin.Helpers;

namespace RazeWinComTr.Areas.Admin.DbModel
{
    [Table("RZW_SAVINGS_ACCOUNT")]
    public class RzwSavingsAccount
    {
        [Key]
        [Column("ID")]
        public int Id { get; set; }

        [Required]
        [Column("USER_ID")]
        public int UserId { get; set; }

        [Required]
        [Column("PLAN_ID")]
        public int PlanId { get; set; }

        [Required]
        [Column("RZW_AMOUNT", TypeName = "decimal(20,8)")]
        public decimal RzwAmount { get; set; }

        [Required]
        [Column("INTEREST_RATE", TypeName = "decimal(10,6)")]
        public decimal InterestRate { get; set; }

        [Required]
        [Column("TERM_TYPE")]
        [StringLength(20)]
        public string TermType { get; set; } = string.Empty;

        [Required]
        [Column("TERM_DURATION")]
        public int TermDuration { get; set; }

        [Required]
        [Column("START_DATE", TypeName = "datetime")]
        public DateTime StartDate { get; set; } = DateTime.UtcNow;

        [Required]
        [Column("MATURITY_DATE", TypeName = "datetime")]
        public DateTime MaturityDate { get; set; }

        [Required]
        [Column("STATUS")]
        [StringLength(20)]
        public string Status { get; set; } = RzwSavingsStatus.Active;

        [Required]
        [Column("TOTAL_EARNED_RZW", TypeName = "decimal(20,8)")]
        public decimal TotalEarnedRzw { get; set; } = 0;

        [Column("LAST_INTEREST_DATE", TypeName = "datetime")]
        public DateTime? LastInterestDate { get; set; }

        [Required]
        [Column("AUTO_RENEW")]
        public bool AutoRenew { get; set; } = false;

        [Required]
        [Column("EARLY_WITHDRAWAL_PENALTY", TypeName = "decimal(10,6)")]
        public decimal EarlyWithdrawalPenalty { get; set; }

        [Required]
        [Column("CR_DATE", TypeName = "datetime")]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        [Column("MOD_DATE", TypeName = "datetime")]
        public DateTime? ModifiedDate { get; set; }

        [Column("CLOSED_DATE", TypeName = "datetime")]
        public DateTime? ClosedDate { get; set; }

        // Navigation properties
        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        [ForeignKey("PlanId")]
        public virtual RzwSavingsPlan Plan { get; set; } = null!;

        public virtual ICollection<RzwSavingsInterestPayment> InterestPayments { get; set; } = new List<RzwSavingsInterestPayment>();

        public virtual ICollection<Trade> Trades { get; set; } = new List<Trade>();

        // Computed properties
        [NotMapped]
        public bool IsActive => Status == RzwSavingsStatus.Active;

        public bool IsMatured(DateTime? currentDate)
        {
            var calculationDate = currentDate ?? DateTime.UtcNow;
            return calculationDate >= MaturityDate && Status == RzwSavingsStatus.Active;
        }
        [NotMapped]
        public int DaysHeld => (DateTime.UtcNow - StartDate).Days;

        [NotMapped]
        public int DaysToMaturity => Math.Max(0, (MaturityDate - DateTime.UtcNow).Days);

        /// <summary>
        /// Gets the maturity amount (principal + total earned interest)
        /// For all accounts, calculates as RzwAmount + TotalEarnedRzw
        /// </summary>
        [NotMapped]
        public decimal MaturityAmount => RzwAmount + TotalEarnedRzw;

        // UI Display Properties - calculated for view rendering
        [NotMapped]
        public bool IsMaturedForDisplay => DateTime.UtcNow >= MaturityDate;

        [NotMapped]
        public TimeSpan TimeRemainingForDisplay => MaturityDate - DateTime.UtcNow;

        [NotMapped]
        public int DaysRemainingForDisplay => IsMaturedForDisplay ? 0 : Math.Max(0, (int)Math.Ceiling(TimeRemainingForDisplay.TotalDays));

        [NotMapped]
        public int TotalDaysForDisplay => (MaturityDate - StartDate).Days;

        [NotMapped]
        public double ProgressPercentageForDisplay => (double)RzwSavingsCalculationHelper.CalculatePreciseProgressPercentage(StartDate, MaturityDate);
    }
}
