using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;

namespace RazeWinComTr.Tests.Unit.Services;

/// <summary>
/// Tests to ensure that existing savings accounts are protected from plan changes
/// and maintain their original snapshot values regardless of plan modifications
/// </summary>
public class RzwSavingsAccountSnapshotProtectionTests : IDisposable
{
    private readonly AppDbContext _context;
    private readonly Mock<IRzwSavingsPlanService> _mockPlanService;
    private readonly Mock<IRzwWalletBalanceManagementService> _mockBalanceService;

    private readonly Mock<ILogger<RzwSavingsService>> _mockLogger;
    private readonly Mock<ILogger<RzwSavingsInterestService>> _mockInterestLogger;
    private readonly Mock<IStringLocalizer<SharedResource>> _mockLocalizer;
    private readonly RzwSavingsService _savingsService;
    private readonly RzwSavingsInterestService _interestService;

    public RzwSavingsAccountSnapshotProtectionTests()
    {
        var options = new DbContextOptionsBuilder<AppDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .ConfigureWarnings(x => x.Ignore(InMemoryEventId.TransactionIgnoredWarning))
            .Options;

        _context = new AppDbContext(options);
        _mockPlanService = new Mock<IRzwSavingsPlanService>();
        _mockBalanceService = new Mock<IRzwWalletBalanceManagementService>();
        _mockLogger = new Mock<ILogger<RzwSavingsService>>();
        _mockInterestLogger = new Mock<ILogger<RzwSavingsInterestService>>();
        _mockLocalizer = new Mock<IStringLocalizer<SharedResource>>();

        // Setup localizer
        _mockLocalizer.Setup(x => x[It.IsAny<string>()])
            .Returns((string key) => new LocalizedString(key, key));
        _interestService = new RzwSavingsInterestService(
            _context,
            _mockBalanceService.Object,
            _mockPlanService.Object,
            _mockInterestLogger.Object);
        _savingsService = new RzwSavingsService(
            _context,
            _mockBalanceService.Object,
            _mockPlanService.Object,
            _interestService,
            null!, // TokenPriceService not needed for these tests
            _mockLocalizer.Object,
            _mockLogger.Object);


    }

    public void Dispose()
    {
        _context.Dispose();
    }

    #region Plan Change Protection Tests

    [Fact]
    public async Task ExistingAccount_WhenPlanInterestRateChanges_ShouldMaintainOriginalRate()
    {
        // Arrange - Create original plan
        var originalPlan = new RzwSavingsPlan
        {
            Id = 1,
            Name = "Monthly Plan",
            TermType = "Monthly",
            TermDuration = 30,
            InterestRate = 0.01m, // Original 1% rate
            MinRzwAmount = 1000m,
            MaxRzwAmount = 100000m,
            IsActive = true,
            CreatedDate = DateTime.UtcNow.AddDays(-10)
        };

        await _context.RzwSavingsPlans.AddAsync(originalPlan);
        await _context.SaveChangesAsync();

        // Setup mocks for account creation
        _mockPlanService.Setup(x => x.GetPlanByIdAsync(originalPlan.Id))
            .ReturnsAsync(originalPlan);
        _mockPlanService.Setup(x => x.ValidatePlanAsync(originalPlan.Id, 5000m)).ReturnsAsync(true);
        _mockBalanceService.Setup(x => x.HasSufficientAvailableRzwAsync(1, 5000m)).ReturnsAsync(true);
        _mockBalanceService.Setup(x => x.LockRzwForSavingsAsync(1, 5000m, It.IsAny<string>(), _context, It.IsAny<int>())).ReturnsAsync(true);

        // Act 1 - Create savings account with original plan
        var createResult = await _savingsService.CreateSavingsAccountAsync(1, originalPlan.Id, 5000m);

        // Assert account creation
        Assert.True(createResult.Success);
        Assert.NotNull(createResult.Account);
        Assert.Equal(0.01m, createResult.Account.InterestRate); // Original rate stored

        // Act 2 - Simulate plan change (admin changes the plan's interest rate)
        originalPlan.InterestRate = 0.02m; // Changed to 2%
        originalPlan.ModifiedDate = DateTime.UtcNow;
        _context.RzwSavingsPlans.Update(originalPlan);
        await _context.SaveChangesAsync();

        // Act 3 - Calculate interest for existing account
        var dailyInterest = await _interestService.CalculateDailyInterestAsync(createResult.Account);

        // Assert - Account should still use original 0.01m rate, not the new 0.02m rate
        var expectedDailyInterest = 5000m * (0.01m / 30); // Using original rate
        Assert.True(Math.Abs(dailyInterest - expectedDailyInterest) < 0.001m,
            $"Expected {expectedDailyInterest:N8}, got {dailyInterest:N8}. Account should use original rate 0.01, not modified rate 0.02");

        // Verify the account still has the original snapshot values
        var accountFromDb = await _context.RzwSavingsAccounts.FindAsync(createResult.Account.Id);
        Assert.Equal(0.01m, accountFromDb!.InterestRate); // Original rate preserved
        Assert.Equal("Monthly", accountFromDb.TermType); // Original term type preserved
        Assert.Equal(30, accountFromDb.TermDuration); // Original duration preserved
    }

    [Fact]
    public async Task ExistingAccount_WhenPlanTermDurationChanges_ShouldMaintainOriginalDuration()
    {
        // Arrange - Create original plan
        var originalPlan = new RzwSavingsPlan
        {
            Id = 2,
            Name = "Quarterly Plan",
            TermType = "Monthly",
            TermDuration = 90, // Original 90 days
            InterestRate = 0.03m,
            MinRzwAmount = 1000m,
            MaxRzwAmount = 100000m,
            IsActive = true,
            CreatedDate = DateTime.UtcNow.AddDays(-5)
        };

        await _context.RzwSavingsPlans.AddAsync(originalPlan);
        await _context.SaveChangesAsync();

        // Setup mocks
        _mockPlanService.Setup(x => x.GetPlanByIdAsync(2))
            .ReturnsAsync(originalPlan);
        _mockPlanService.Setup(x => x.ValidatePlanAsync(2, 3000m))
            .ReturnsAsync(true);
        _mockBalanceService.Setup(x => x.HasSufficientAvailableRzwAsync(1, 3000m))
            .ReturnsAsync(true);
        _mockBalanceService.Setup(x => x.LockRzwForSavingsAsync(1, 3000m, It.IsAny<string>(), _context, It.IsAny<int>()))
            .ReturnsAsync(true);

        // Act 1 - Create savings account
        var createResult = await _savingsService.CreateSavingsAccountAsync(1, 2, 3000m);

        // Assert account creation
        Assert.True(createResult.Success);
        Assert.NotNull(createResult.Account);
        var originalMaturityDate = createResult.Account.MaturityDate;
        Assert.Equal(90, createResult.Account.TermDuration); // Original duration stored

        // Act 2 - Simulate plan change (admin changes the plan's term duration)
        originalPlan.TermDuration = 120; // Changed to 120 days
        originalPlan.ModifiedDate = DateTime.UtcNow;
        _context.RzwSavingsPlans.Update(originalPlan);
        await _context.SaveChangesAsync();

        // Assert - Account should maintain original duration and maturity date
        var accountFromDb = await _context.RzwSavingsAccounts.FindAsync(createResult.Account.Id);
        Assert.Equal(90, accountFromDb!.TermDuration); // Original duration preserved
        Assert.Equal(originalMaturityDate, accountFromDb.MaturityDate); // Original maturity date preserved

        // Verify that the account's maturity calculation is based on original duration
        var expectedMaturityDate = createResult.Account.StartDate.AddDays(90); // Original 90 days
        Assert.True(Math.Abs((accountFromDb.MaturityDate - expectedMaturityDate).TotalHours) < 1,
            "Maturity date should be calculated from original duration, not modified plan duration");
    }

    [Fact]
    public async Task ExistingAccount_WhenPlanTermTypeChanges_ShouldMaintainOriginalTermType()
    {
        // Arrange - Create original plan
        var originalPlan = new RzwSavingsPlan
        {
            Id = 3,
            Name = "Flexible Plan",
            TermType = "Daily", // Original term type
            TermDuration = 30,
            InterestRate = 0.0005m,
            MinRzwAmount = 1000m,
            MaxRzwAmount = 100000m,
            IsActive = true,
            CreatedDate = DateTime.UtcNow.AddDays(-3)
        };

        await _context.RzwSavingsPlans.AddAsync(originalPlan);
        await _context.SaveChangesAsync();

        // Setup mocks
        _mockPlanService.Setup(x => x.GetPlanByIdAsync(3))
            .ReturnsAsync(originalPlan);
        _mockPlanService.Setup(x => x.ValidatePlanAsync(3, 2000m))
            .ReturnsAsync(true);
        _mockBalanceService.Setup(x => x.HasSufficientAvailableRzwAsync(1, 2000m))
            .ReturnsAsync(true);
        _mockBalanceService.Setup(x => x.LockRzwForSavingsAsync(1, 2000m, It.IsAny<string>(), _context, It.IsAny<int>()))
            .ReturnsAsync(true);

        // Act 1 - Create savings account
        var createResult = await _savingsService.CreateSavingsAccountAsync(1, 3, 2000m);

        // Assert account creation
        Assert.True(createResult.Success);
        Assert.NotNull(createResult.Account);
        Assert.Equal("Daily", createResult.Account.TermType); // Original term type stored

        // Act 2 - Simulate plan change (admin changes the plan's term type)
        originalPlan.TermType = "Monthly"; // Changed from Daily to Monthly
        originalPlan.ModifiedDate = DateTime.UtcNow;
        _context.RzwSavingsPlans.Update(originalPlan);
        await _context.SaveChangesAsync();

        // Act 3 - Calculate interest for existing account
        var dailyInterest = await _interestService.CalculateDailyInterestAsync(createResult.Account);

        // Assert - Account should still use original "Daily" term type for calculations
        var expectedDailyInterest = 2000m * 0.0005m; // Daily rate calculation
        Assert.True(Math.Abs(dailyInterest - expectedDailyInterest) < 0.001m,
            $"Expected {expectedDailyInterest:N8}, got {dailyInterest:N8}. Account should use original Daily term type");

        // Verify the account still has the original snapshot values
        var accountFromDb = await _context.RzwSavingsAccounts.FindAsync(createResult.Account.Id);
        Assert.Equal("Daily", accountFromDb!.TermType); // Original term type preserved
        Assert.Equal(0.0005m, accountFromDb.InterestRate); // Original rate preserved
        Assert.Equal(30, accountFromDb.TermDuration); // Original duration preserved
    }

    #endregion

    #region Multiple Accounts at Different Times Tests

    [Fact]
    public async Task MultipleAccounts_CreatedAtDifferentTimes_ShouldMaintainTheirRespectiveSnapshots()
    {
        // Arrange - Create a plan that will be modified over time
        var plan = new RzwSavingsPlan
        {
            Id = 4,
            Name = "Evolution Plan",
            TermType = "Monthly",
            TermDuration = 60,
            InterestRate = 0.01m, // Initial rate
            MinRzwAmount = 1000m,
            MaxRzwAmount = 100000m,
            IsActive = true,
            CreatedDate = DateTime.UtcNow.AddDays(-30)
        };

        await _context.RzwSavingsPlans.AddAsync(plan);
        await _context.SaveChangesAsync();

        // Setup mocks for all account creations
        _mockPlanService.Setup(x => x.GetPlanByIdAsync(4))
            .ReturnsAsync(plan);
        _mockPlanService.Setup(x => x.ValidatePlanAsync(4, It.IsAny<decimal>()))
            .ReturnsAsync(true);
        _mockBalanceService.Setup(x => x.HasSufficientAvailableRzwAsync(It.IsAny<int>(), It.IsAny<decimal>()))
            .ReturnsAsync(true);
        _mockBalanceService.Setup(x => x.LockRzwForSavingsAsync(It.IsAny<int>(), It.IsAny<decimal>(), It.IsAny<string>(), _context, It.IsAny<int>()))
            .ReturnsAsync(true);

        // Act 1 - Create first account with original plan values
        var account1Result = await _savingsService.CreateSavingsAccountAsync(1, 4, 1000m);
        Assert.True(account1Result.Success);
        var account1 = account1Result.Account!;

        // Simulate time passing and plan changes
        await Task.Delay(10); // Small delay to ensure different timestamps

        // Act 2 - Modify plan (first change)
        plan.InterestRate = 0.015m; // Increased to 1.5%
        plan.TermDuration = 45; // Reduced to 45 days
        plan.ModifiedDate = DateTime.UtcNow;
        _context.RzwSavingsPlans.Update(plan);
        await _context.SaveChangesAsync();

        // Act 3 - Create second account with modified plan values
        var account2Result = await _savingsService.CreateSavingsAccountAsync(2, 4, 2000m);
        Assert.True(account2Result.Success);
        var account2 = account2Result.Account!;

        await Task.Delay(10); // Small delay

        // Act 4 - Modify plan again (second change)
        plan.InterestRate = 0.02m; // Increased to 2%
        plan.TermDuration = 30; // Reduced to 30 days
        plan.TermType = "Daily"; // Changed term type
        plan.ModifiedDate = DateTime.UtcNow;
        _context.RzwSavingsPlans.Update(plan);
        await _context.SaveChangesAsync();

        // Act 5 - Create third account with latest plan values
        var account3Result = await _savingsService.CreateSavingsAccountAsync(3, 4, 3000m);
        Assert.True(account3Result.Success);
        var account3 = account3Result.Account!;

        // Assert - Each account should maintain its snapshot values
        var accountsFromDb = await _context.RzwSavingsAccounts
            .Where(a => a.PlanId == 4)
            .OrderBy(a => a.Id)
            .ToListAsync();

        Assert.Equal(3, accountsFromDb.Count);

        // Account 1 - Original values
        Assert.Equal(0.01m, accountsFromDb[0].InterestRate);
        Assert.Equal(60, accountsFromDb[0].TermDuration);
        Assert.Equal("Monthly", accountsFromDb[0].TermType);

        // Account 2 - First modification values
        Assert.Equal(0.015m, accountsFromDb[1].InterestRate);
        Assert.Equal(45, accountsFromDb[1].TermDuration);
        Assert.Equal("Monthly", accountsFromDb[1].TermType);

        // Account 3 - Latest values
        Assert.Equal(0.02m, accountsFromDb[2].InterestRate);
        Assert.Equal(30, accountsFromDb[2].TermDuration);
        Assert.Equal("Daily", accountsFromDb[2].TermType);

        // Verify interest calculations use respective snapshot values
        var interest1 = await _interestService.CalculateDailyInterestAsync(accountsFromDb[0]);
        var interest2 = await _interestService.CalculateDailyInterestAsync(accountsFromDb[1]);
        var interest3 = await _interestService.CalculateDailyInterestAsync(accountsFromDb[2]);

        // Each should calculate based on their own snapshot values
        var expected1 = 1000m * (0.01m / 30); // Monthly calculation with original rate
        var expected2 = 2000m * (0.015m / 30); // Monthly calculation with first modified rate
        var expected3 = 3000m * 0.02m; // Daily calculation with latest rate

        Assert.True(Math.Abs(interest1 - expected1) < 0.001m);
        Assert.True(Math.Abs(interest2 - expected2) < 0.001m);
        Assert.True(Math.Abs(interest3 - expected3) < 0.001m);
    }

    #endregion
}
