using RazeWinComTr.Areas.Admin.ViewModels.RzwDistributionReports;

namespace RazeWinComTr.Areas.Admin.Services.Interfaces
{
    /// <summary>
    /// Interface for RZW Distribution Reports Service operations
    /// Provides reporting functionality for RZW distributions through savings interest and referral rewards
    /// </summary>
    public interface IRzwDistributionReportsService
    {
        /// <summary>
        /// Gets comprehensive RZW distribution report for a date range
        /// </summary>
        /// <param name="startDate">Start date for the report</param>
        /// <param name="endDate">End date for the report</param>
        /// <returns>RZW distribution report with breakdown by source</returns>
        Task<RzwDistributionReportViewModel> GetDistributionReportAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// Gets savings interest distribution data for a date range
        /// </summary>
        /// <param name="startDate">Start date for the report</param>
        /// <param name="endDate">End date for the report</param>
        /// <returns>Savings interest distribution data</returns>
        Task<SavingsInterestDistributionViewModel> GetSavingsInterestDistributionAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// Gets referral reward distribution data for a date range
        /// </summary>
        /// <param name="startDate">Start date for the report</param>
        /// <param name="endDate">End date for the report</param>
        /// <returns>Referral reward distribution data</returns>
        Task<ReferralRewardDistributionViewModel> GetReferralRewardDistributionAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// Gets daily RZW distribution summary for a date range
        /// </summary>
        /// <param name="startDate">Start date for the report</param>
        /// <param name="endDate">End date for the report</param>
        /// <returns>Daily distribution summary</returns>
        Task<List<DailyDistributionSummaryViewModel>> GetDailyDistributionSummaryAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// Gets weekly RZW distribution summary for a date range
        /// </summary>
        /// <param name="startDate">Start date for the report</param>
        /// <param name="endDate">End date for the report</param>
        /// <returns>Weekly distribution summary</returns>
        Task<List<WeeklyDistributionSummaryViewModel>> GetWeeklyDistributionSummaryAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// Gets monthly RZW distribution summary for a date range
        /// </summary>
        /// <param name="startDate">Start date for the report</param>
        /// <param name="endDate">End date for the report</param>
        /// <returns>Monthly distribution summary</returns>
        Task<List<MonthlyDistributionSummaryViewModel>> GetMonthlyDistributionSummaryAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// Gets yearly RZW distribution summary for a date range
        /// </summary>
        /// <param name="startDate">Start date for the report</param>
        /// <param name="endDate">End date for the report</param>
        /// <returns>Yearly distribution summary</returns>
        Task<List<YearlyDistributionSummaryViewModel>> GetYearlyDistributionSummaryAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// Gets total RZW distributed across all sources for a date range
        /// </summary>
        /// <param name="startDate">Start date for the calculation</param>
        /// <param name="endDate">End date for the calculation</param>
        /// <returns>Total RZW amount distributed</returns>
        Task<decimal> GetTotalRzwDistributedAsync(DateTime startDate, DateTime endDate);
    }
}
