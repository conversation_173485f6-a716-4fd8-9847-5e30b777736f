using RazeWinComTr.Areas.Admin.Enums;

namespace RazeWinComTr.Areas.MyAccount.ViewModels;

/// <summary>
/// Generic verification status and UI information for profile page
/// </summary>
public class VerificationViewModel
{
    /// <summary>
    /// Type of verification (Email, Phone, Address, etc.)
    /// </summary>
    public VerificationType VerificationType { get; set; }

    /// <summary>
    /// Whether the verification is completed
    /// </summary>
    public bool IsVerified { get; set; }

    /// <summary>
    /// Date when verification was completed (if verified)
    /// </summary>
    public DateTime? VerifiedDate { get; set; }

    /// <summary>
    /// Whether user can send a verification now (not rate limited)
    /// </summary>
    public bool CanSendVerification { get; set; }

    /// <summary>
    /// Time remaining until user can send another verification
    /// </summary>
    public TimeSpan? TimeUntilNext { get; set; }

    /// <summary>
    /// Number of verification attempts made today
    /// </summary>
    public int TodayAttempts { get; set; }

    /// <summary>
    /// Maximum number of verifications allowed per day
    /// </summary>
    public int MaxDailyAttempts { get; set; } = 5;

    /// <summary>
    /// Minutes that must pass between verification attempts
    /// </summary>
    public int MinutesBetweenAttempts { get; set; } = 5;

    /// <summary>
    /// Target value being verified (email address, phone number, etc.)
    /// </summary>
    public string TargetValue { get; set; } = string.Empty;

    /// <summary>
    /// Gets a user-friendly status message for display
    /// </summary>
    public string GetStatusMessage()
    {
        if (IsVerified)
        {
            return $"Verified on {VerifiedDate:dd/MM/yyyy}";
        }

        if (!CanSendVerification)
        {
            if (TimeUntilNext.HasValue)
            {
                var minutes = (int)Math.Ceiling(TimeUntilNext.Value.TotalMinutes);
                return $"Next attempt in {minutes} minutes";
            }
            else
            {
                return $"Daily limit reached ({TodayAttempts}/{MaxDailyAttempts})";
            }
        }

        return GetNotVerifiedText();
    }

    /// <summary>
    /// Gets CSS class for status display
    /// </summary>
    public string GetStatusCssClass()
    {
        return IsVerified ? "text-success" : "text-warning";
    }

    /// <summary>
    /// Gets icon class for status display
    /// </summary>
    public string GetStatusIconClass()
    {
        return IsVerified ? "fas fa-check-circle" : "fas fa-exclamation-triangle";
    }

    /// <summary>
    /// Gets verification type specific "not verified" text
    /// </summary>
    private string GetNotVerifiedText()
    {
        return VerificationType switch
        {
            VerificationType.Email => "Email not verified",
            VerificationType.Phone => "Phone not verified",
            VerificationType.Address => "Address not verified",
            VerificationType.Identity => "Identity not verified",
            VerificationType.BankAccount => "Bank account not verified",
            VerificationType.TwoFactor => "Two-factor not enabled",
            _ => "Not verified"
        };
    }
}

/// <summary>
/// Email-specific verification view model for convenience
/// </summary>
public class EmailVerificationViewModel : VerificationViewModel
{
    public EmailVerificationViewModel()
    {
        VerificationType = VerificationType.Email;
    }

    // Convenience property for email address
    public string EmailAddress
    {
        get => TargetValue;
        set => TargetValue = value;
    }
}
