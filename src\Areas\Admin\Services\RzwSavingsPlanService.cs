using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services.Interfaces;

namespace RazeWinComTr.Areas.Admin.Services;

/// <summary>
/// Service for managing RZW savings plans
/// </summary>
public class RzwSavingsPlanService : IRzwSavingsPlanService
{
    private readonly AppDbContext _context;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly ILogger<RzwSavingsPlanService> _logger;

    public RzwSavingsPlanService(
        AppDbContext context,
        IStringLocalizer<SharedResource> localizer,
        ILogger<RzwSavingsPlanService> logger)
    {
        _context = context;
        _localizer = localizer;
        _logger = logger;
    }

    /// <summary>
    /// Gets all active savings plans ordered by display order
    /// </summary>
    /// <returns>List of active savings plans</returns>
    public async Task<List<RzwSavingsPlan>> GetActivePlansAsync()
    {
        return await _context.RzwSavingsPlans
            .Where(p => p.IsActive)
            .OrderBy(p => p.DisplayOrder)
            .ToListAsync();
    }

    /// <summary>
    /// Gets a savings plan by ID
    /// </summary>
    /// <param name="planId">The plan ID</param>
    /// <returns>The savings plan or null if not found</returns>
    public async Task<RzwSavingsPlan?> GetPlanByIdAsync(int planId)
    {
        return await _context.RzwSavingsPlans
            .FirstOrDefaultAsync(p => p.Id == planId && p.IsActive);
    }

    /// <summary>
    /// Gets a savings plan by term type
    /// </summary>
    /// <param name="termType">The term type</param>
    /// <returns>The savings plan or null if not found</returns>
    public async Task<RzwSavingsPlan?> GetPlanByTermTypeAsync(string termType)
    {
        return await _context.RzwSavingsPlans
            .FirstOrDefaultAsync(p => p.TermType == termType && p.IsActive);
    }

    /// <summary>
    /// Validates if a plan and amount combination is valid
    /// </summary>
    /// <param name="planId">The plan ID</param>
    /// <param name="amount">The RZW amount</param>
    /// <returns>True if valid, false otherwise</returns>
    public async Task<bool> ValidatePlanAsync(int planId, decimal amount)
    {
        var plan = await GetPlanByIdAsync(planId);
        if (plan == null) return false;

        if (amount < plan.MinRzwAmount) return false;
        if (plan.MaxRzwAmount.HasValue && amount > plan.MaxRzwAmount.Value) return false;

        return true;
    }

    /// <summary>
    /// Initializes default savings plans if none exist
    /// </summary>
    public async Task InitializeDefaultPlansAsync()
    {
        if (await _context.RzwSavingsPlans.AnyAsync()) return;

        var plans = new List<RzwSavingsPlan>
        {
            new()
            {
                Name = "RZW Daily Savings",
                TermType = RzwSavingsTermType.Daily,
                TermDuration = RzwSavingsConstants.DAILY_TERM_DURATION,
                InterestRate = 0.0003m, // %0.03 günlük
                MinRzwAmount = 100m,
                MaxRzwAmount = 1000000m,
                IsActive = true,
                DisplayOrder = 1,
                Description = "Günlük vadeli RZW hesabı. %0.03 günlük faiz (Yıllık ~%11)",
                CreatedDate = DateTime.UtcNow
            },
            new()
            {
                Name = "RZW Monthly Savings",
                TermType = RzwSavingsTermType.Monthly,
                TermDuration = RzwSavingsConstants.MONTHLY_TERM_DURATION,
                InterestRate = 0.01m, // %1 aylık
                MinRzwAmount = 500m,
                MaxRzwAmount = 5000000m,
                IsActive = true,
                DisplayOrder = 2,
                Description = "Aylık vadeli RZW hesabı. %1 aylık faiz (Yıllık ~%12)",
                CreatedDate = DateTime.UtcNow
            },
            new()
            {
                Name = "RZW Yearly Savings",
                TermType = RzwSavingsTermType.Yearly,
                TermDuration = RzwSavingsConstants.YEARLY_TERM_DURATION,
                InterestRate = 0.15m, // %15 yıllık
                MinRzwAmount = 1000m,
                MaxRzwAmount = 10000000m,
                IsActive = true,
                DisplayOrder = 3,
                Description = "Yıllık vadeli RZW hesabı. %15 yıllık faiz",
                CreatedDate = DateTime.UtcNow
            }
        };

        _context.RzwSavingsPlans.AddRange(plans);
        await _context.SaveChangesAsync();

        _logger.LogInformation("Default RZW savings plans initialized");
    }

    /// <summary>
    /// Gets all savings plans for admin panel
    /// </summary>
    /// <returns>List of all savings plans</returns>
    public async Task<List<RzwSavingsPlan>> GetAllPlansAsync()
    {
        return await _context.RzwSavingsPlans
            .OrderBy(p => p.DisplayOrder)
            .ThenBy(p => p.Name)
            .ToListAsync();
    }

    /// <summary>
    /// Creates a new savings plan
    /// </summary>
    /// <param name="plan">The plan to create</param>
    /// <returns>The created plan</returns>
    public async Task<RzwSavingsPlan> CreatePlanAsync(RzwSavingsPlan plan)
    {
        _context.RzwSavingsPlans.Add(plan);
        await _context.SaveChangesAsync();
        _logger.LogInformation("Created new RZW savings plan: {PlanName}", plan.Name);
        return plan;
    }

    /// <summary>
    /// Updates an existing savings plan
    /// </summary>
    /// <param name="plan">The plan to update</param>
    /// <returns>The updated plan</returns>
    public async Task<RzwSavingsPlan> UpdatePlanAsync(RzwSavingsPlan plan)
    {
        _context.RzwSavingsPlans.Update(plan);
        await _context.SaveChangesAsync();
        _logger.LogInformation("Updated RZW savings plan: {PlanName}", plan.Name);
        return plan;
    }

    /// <summary>
    /// Deletes a savings plan
    /// </summary>
    /// <param name="planId">The plan ID to delete</param>
    /// <returns>True if deleted, false if not found</returns>
    public async Task<bool> DeletePlanAsync(int planId)
    {
        var plan = await GetPlanByIdAsync(planId);
        if (plan == null) return false;

        _context.RzwSavingsPlans.Remove(plan);
        await _context.SaveChangesAsync();
        _logger.LogInformation("Deleted RZW savings plan: {PlanName}", plan.Name);
        return true;
    }

    /// <summary>
    /// Gets statistics for a specific plan
    /// </summary>
    /// <param name="planId">The plan ID</param>
    /// <returns>Plan statistics</returns>
    public async Task<(int TotalAccounts, decimal TotalLockedRzw, decimal TotalInterestPaid)> GetPlanStatisticsAsync(int planId)
    {
        var totalAccounts = await _context.RzwSavingsAccounts
            .Where(a => a.PlanId == planId)
            .CountAsync();

        var totalLockedRzw = await _context.RzwSavingsAccounts
            .Where(a => a.PlanId == planId && a.Status == RzwSavingsStatus.Active)
            .SumAsync(a => a.RzwAmount);

        var totalInterestPaid = await _context.RzwSavingsInterestPayments
            .Where(p => p.RzwSavingsAccount.PlanId == planId)
            .SumAsync(p => p.RzwAmount);

        return (totalAccounts, totalLockedRzw, totalInterestPaid);
    }

    /// <summary>
    /// Validates if a plan exists and is active
    /// </summary>
    /// <param name="planId">The plan ID</param>
    /// <returns>True if plan is valid and active</returns>
    public async Task<bool> IsPlanValidAsync(int planId)
    {
        var plan = await GetPlanByIdAsync(planId);
        return plan != null && plan.IsActive;
    }

    /// <summary>
    /// Deactivates a savings plan
    /// </summary>
    /// <param name="planId">The plan ID to deactivate</param>
    /// <returns>True if successful</returns>
    public async Task<bool> DeactivatePlanAsync(int planId)
    {
        var plan = await GetPlanByIdAsync(planId);
        if (plan == null) return false;

        plan.IsActive = false;
        plan.ModifiedDate = DateTime.UtcNow;

        _context.RzwSavingsPlans.Update(plan);
        await _context.SaveChangesAsync();
        _logger.LogInformation("Deactivated RZW savings plan: {PlanName}", plan.Name);
        return true;
    }
}
