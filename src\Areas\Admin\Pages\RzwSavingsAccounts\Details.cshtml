@page
@using Microsoft.Extensions.Localization
@model RazeWinComTr.Areas.Admin.Pages.RzwSavingsAccounts.DetailsModel
@inject IStringLocalizer<SharedResource> L

@{
    ViewData["Title"] = L["Account Details"];
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@L["Account Details"] #@Model.Account.Id</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item"><a href="#">@L["RZW Savings"]</a></li>
                    <li class="breadcrumb-item"><a href="/Admin/RzwSavingsAccounts">@L["Savings Accounts"]</a></li>
                    <li class="breadcrumb-item active">@L["Details"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <div class="row">
            <!-- Account Information -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">@L["Account Information"]</h3>
                    </div>
                    <div class="card-body">
                        <dl class="row">
                            <dt class="col-sm-3">@L["User"]</dt>
                            <dd class="col-sm-9">@Model.Account.UserEmail</dd>

                            <dt class="col-sm-3">@L["Plan"]</dt>
                            <dd class="col-sm-9">@Model.Account.PlanName</dd>

                            <dt class="col-sm-3">@L["Amount"]</dt>
                            <dd class="col-sm-9">@Model.Account.RzwAmount.ToString("N8") RZW</dd>

                            <dt class="col-sm-3">@L["Interest Rate"]</dt>
                            <dd class="col-sm-9">@Model.Account.InterestRateDisplayText</dd>

                            <dt class="col-sm-3">@L["Term"]</dt>
                            <dd class="col-sm-9">@Model.Account.GetLocalizedTermDisplayText(Model.Localizer)</dd>

                            <dt class="col-sm-3">@L["Start Date"]</dt>
                            <dd class="col-sm-9">@Model.Account.StartDate.ToString("yyyy-MM-dd HH:mm")</dd>

                            <dt class="col-sm-3">@L["Maturity Date"]</dt>
                            <dd class="col-sm-9">@Model.Account.MaturityDate.ToString("yyyy-MM-dd HH:mm")</dd>

                            <dt class="col-sm-3">@L["Status"]</dt>
                            <dd class="col-sm-9">
                                <span class="badge @Model.Account.StatusBadgeClass">
                                    @Model.Account.StatusDisplayText
                                </span>
                            </dd>

                            <dt class="col-sm-3">@L["Total Earned"]</dt>
                            <dd class="col-sm-9">@Model.Account.TotalEarnedRzw.ToString("N8") RZW</dd>

                            <dt class="col-sm-3">@L["Maturity Amount"]</dt>
                            <dd class="col-sm-9">@Model.Account.MaturityAmount.ToString("N8") RZW</dd>

                            <dt class="col-sm-3">@L["Auto Renew"]</dt>
                            <dd class="col-sm-9">@(Model.Account.AutoRenew ? L["Yes"] : L["No"])</dd>

                            @if (Model.Account.LastInterestDate.HasValue)
                            {
                                <dt class="col-sm-3">@L["Last Interest"]</dt>
                                <dd class="col-sm-9">@Model.Account.LastInterestDate.Value.ToString("yyyy-MM-dd HH:mm")</dd>
                            }
                        </dl>
                    </div>
                </div>

                <!-- Interest History -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">@L["Interest History"]</h3>
                    </div>
                    <div class="card-body">
                        @if (Model.InterestHistory.Any())
                        {
                            <div class="table-responsive">
                                <table class="table table-sm table-striped">
                                    <thead>
                                    <tr>
                                        <th>@L["Date"]</th>
                                        <th>@L["Amount"]</th>
                                        <th>@L["Daily Rate"]</th>
                                        <th>@L["Principal"]</th>
                                        <th>@L["Description"]</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    @foreach (var payment in Model.InterestHistory.OrderByDescending(p => p.PaymentDate))
                                    {
                                        <tr>
                                            <td>@payment.PaymentDate.ToString("yyyy-MM-dd HH:mm")</td>
                                            <td>@payment.RzwAmount.ToString("N8")</td>
                                            <td>@(payment.DailyRate?.ToString("P6") ?? "-")</td>
                                            <td>@(payment.PrincipalAmount?.ToString("N8") ?? "-")</td>
                                            <td>@payment.Description</td>
                                        </tr>
                                    }
                                    </tbody>
                                </table>
                            </div>
                        }
                        else
                        {
                            <p class="text-muted">@L["No interest payments yet"]</p>
                        }
                    </div>
                </div>
            </div>

            <!-- Progress and Actions -->
            <div class="col-md-4">
                <!-- Progress Card -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">@L["Progress"]</h3>
                    </div>
                    <div class="card-body">
                        <div class="progress mb-3">
                            <div class="progress-bar" role="progressbar" 
                                 style="width: @Model.Account.ProgressPercentage%"
                                 aria-valuenow="@Model.Account.ProgressPercentage" 
                                 aria-valuemin="0" aria-valuemax="100">
                                @Model.Account.ProgressPercentage%
                            </div>
                        </div>
                        <p><strong>@L["Days Held"]:</strong> @Model.Account.DaysHeld</p>
                        <p><strong>@L["Days to Maturity"]:</strong> @Model.Account.DaysToMaturity</p>
                    </div>
                </div>

                <!-- Admin Actions -->
                @if (Model.Account.IsActive)
                {
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">@L["Admin Actions"]</h3>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>@L["Warning"]!</strong> @L["These actions are irreversible."]
                            </div>

                            <form method="post" style="display: inline;">
                                <button type="submit" asp-page-handler="ForceMaturity" asp-route-id="@Model.Account.Id" 
                                        class="btn btn-warning btn-block mb-2"
                                        onclick="return confirm('@L["Are you sure you want to force maturity for this account?"]')">
                                    <i class="fas fa-clock"></i> @L["Force Maturity"]
                                </button>
                            </form>

                            <form method="post" style="display: inline;">
                                <button type="submit" asp-page-handler="EmergencyClose" asp-route-id="@Model.Account.Id" 
                                        class="btn btn-danger btn-block"
                                        onclick="return confirm('@L["Are you sure you want to emergency close this account?"]')">
                                    <i class="fas fa-ban"></i> @L["Emergency Close"]
                                </button>
                            </form>
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>
</section>
