@page
@model RazeWinComTr.Areas.MyAccount.Pages.TradeHistoryModel
@using Microsoft.Extensions.Localization
@using RazeWinComTr.Areas.Admin.Helpers
@using RazeWinComTr.Areas.Admin.DbModel
@inject IStringLocalizer<SharedResource> Localizer
@{
    ViewData["Title"] = Localizer["My Trade History"];
}

@section Styles {
    <!-- DataTables CSS from local files -->
    <link rel="stylesheet" href="/plugins/datatables/css/jquery.dataTables.min.css">
    <link rel="stylesheet" href="/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" href="/plugins/datatables-responsive/css/responsive.bootstrap4.min.css">
    <link rel="stylesheet" href="/plugins/datatables-buttons/css/buttons.bootstrap4.min.css">

    <style>
        /* Custom styles for the trade history table */
        .custom-trade-table {
            color: #333;
            background-color: #fff;
            border-collapse: collapse;
            width: 100%;
        }

            .custom-trade-table thead th {
                color: #fff !important;
                background-color: #343a40 !important;
                border-color: #454d55 !important;
                font-weight: bold;
                padding: 10px;
                text-align: left;
            }

            .custom-trade-table tbody td {
                color: #333 !important;
                /* background-color: #fff !important; */
                padding: 8px;
                border: 1px solid #dee2e6;
            }

            .custom-trade-table tfoot th {
                color: #fff !important;
                background-color: #343a40 !important;
                border-color: #454d55 !important;
                font-weight: bold;
                padding: 10px;
            }

            /* Striped rows */
            .custom-trade-table tbody tr:nth-of-type(odd) {
                background-color: rgba(0, 0, 0, 0.05) !important;
            }

            /* Hover effect */
            .custom-trade-table tbody tr:hover {
                color: #333 !important;
                background-color: rgba(0, 0, 0, 0.075) !important;
            }

        /* DataTables specific styling */
        .dataTables_wrapper .dataTables_length,
        .dataTables_wrapper .dataTables_filter,
        .dataTables_wrapper .dataTables_info,
        .dataTables_wrapper .dataTables_processing,
        .dataTables_wrapper .dataTables_paginate {
            color: #333 !important;
            margin-bottom: 10px;
        }

            .dataTables_wrapper .dataTables_paginate .paginate_button {
                color: #333 !important;
                background-color: #f8f9fa !important;
                border: 1px solid #dee2e6 !important;
            }

                .dataTables_wrapper .dataTables_paginate .paginate_button.current,
                .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
                    color: #fff !important;
                    background-color: #007bff !important;
                    border-color: #007bff !important;
                }

        /* Fix for DataTables buttons */
        .dt-buttons .btn {
            color: #333 !important;
            background-color: #f8f9fa !important;
            border: 1px solid #dee2e6 !important;
            margin-right: 5px;
        }

            .dt-buttons .btn:hover {
                color: #333 !important;
                background-color: #e2e6ea !important;
                border-color: #dae0e5 !important;
            }
    </style>
}

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@Localizer["My Trade History"]</h1>
            </div><!-- /.col -->
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/MyAccount/Dashboard">@Localizer["Home"]</a></li>
                    <li class="breadcrumb-item active">@Localizer["My Trade History"]</li>
                </ol>
            </div><!-- /.col -->
        </div><!-- /.row -->
    </div><!-- /.container-fluid -->
</div>
<!-- /.content-header -->
<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">@Localizer["All Trades"]</h3>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body">
                        <table id="example1" class="table table-bordered table-striped custom-trade-table">
                            <thead>
                                <tr>
                                    <th>@Localizer["Date"]</th>
                                    <th>@Localizer["Type"]</th>
                                    <th>@Localizer["Coin"]</th>
                                    <th>@Localizer["Amount"]</th>
                                    <th>@Localizer["Rate"]</th>
                                    <th>@Localizer["Total"]</th>
                                    <th>@Localizer["Description"]</th>
                                    <th>@Localizer["Previous Balance"]</th>
                                    <th>@Localizer["New Balance"]</th>
                                    <th>@Localizer["Previous Wallet Balance"]</th>
                                    <th>@Localizer["New Wallet Balance"]</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model.Trades)
                                {
                                    <tr>
                                        <td data-sort="@item.CreatedDate.ToLocalTime().ToString("yyyy-MM-dd HH:mm:ss")">@DateTimeFormatHelper.FormatForDisplay(item.CreatedDate)</td>
                                        <td>
                                            @if (item.Type.IsCoinInflow())
                                            {
                                                <span class="badge badge-success">@Localizer[item.Type.GetLocalizationKey()]</span>
                                            }
                                            else
                                            {
                                                <span class="badge badge-danger">@Localizer[item.Type.GetLocalizationKey()]</span>
                                            }
                                        </td>
                                        <td>@item.CoinCode</td>
                                        <td class="text-right" style=" @(item.Type.IsCoinInflow() ? "color:green !important;" : "color:red !important;")">@item.CoinAmount.ToString("N8")</td>
                                        <td class="text-right">@item.CoinRate.ToString("N2") ₺</td>
                                        <td class="text-right">@item.TryAmount.ToString("N2") ₺</td>
                                        <td>
                                            @if (item.ReferralReward != null)
                                            {
                                                <div class="text-muted small">
                                                    @item.ReferralReward.GetReferralRewardDescription()
                                                </div>
                                            }
                                        </td>
                                        <td class="text-right">@item.PreviousBalance.ToString("N2") ₺</td>
                                        <td class="text-right">@item.NewBalance.ToString("N2") ₺</td>
                                        <td class="text-right">@item.PreviousWalletBalance.ToString("N8")</td>
                                        <td class="text-right">@item.NewWalletBalance.ToString("N8")</td>
                                    </tr>
                                }
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th>@Localizer["Date"]</th>
                                    <th>@Localizer["Type"]</th>
                                    <th>@Localizer["Coin"]</th>
                                    <th>@Localizer["Amount"]</th>
                                    <th>@Localizer["Rate"]</th>
                                    <th>@Localizer["Total"]</th>
                                    <th>@Localizer["Description"]</th>
                                    <th>@Localizer["Previous Balance"]</th>
                                    <th>@Localizer["New Balance"]</th>
                                    <th>@Localizer["Previous Wallet Balance"]</th>
                                    <th>@Localizer["New Wallet Balance"]</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->
@section Scripts {
    <!-- jQuery first, then Bootstrap, then other libraries -->
    <script src="/plugins/jquery/jquery.min.js"></script>
    <script src="/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="/plugins/datatables/js/jquery.dataTables.min.js"></script>
    <script src="/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js"></script>
    <script src="/plugins/datatables-responsive/js/dataTables.responsive.min.js"></script>
    <script src="/plugins/datatables-responsive/js/responsive.bootstrap4.min.js"></script>
    <script src="/plugins/datatables-buttons/js/dataTables.buttons.min.js"></script>
    <script src="/plugins/datatables-buttons/js/buttons.bootstrap4.min.js"></script>
    <script src="/plugins/jszip/jszip.min.js"></script>
    <script src="/plugins/pdfmake/pdfmake.min.js"></script>
    <script src="/plugins/pdfmake/vfs_fonts.js"></script>
    <script src="/plugins/datatables-buttons/js/buttons.html5.min.js"></script>
    <script src="/plugins/datatables-buttons/js/buttons.print.min.js"></script>
    <script src="/plugins/datatables-buttons/js/buttons.colVis.min.js"></script>

    <script>
        $(document).ready(function() {
            try {
                console.log("DataTable initialization starting...");
                // Add custom sorting for date columns
                $.fn.dataTable.ext.type.detect.unshift(function(data) {
                    // Check if the data has a data-sort attribute (our date column)
                    if ($(data).attr('data-sort')) {
                        return 'date-sort';
                    }
                    return null;
                });

                $.fn.dataTable.ext.type.order['date-sort-pre'] = function(data) {
                    // Extract the ISO date from the data-sort attribute
                    return $(data).attr('data-sort');
                };

                var table = $("#example1").DataTable({
                    "responsive": true,
                    "lengthChange": false,
                    "autoWidth": false,
                    "buttons": ["copy", "csv", "excel", "pdf", "print", "colvis"],
                    "order": [[0, "desc"]],
                    "language": {
                        "search": "@Localizer["Search"]:",
                        "paginate": {
                            "first": "@Localizer["First"]",
                            "last": "@Localizer["Last"]",
                            "next": "@Localizer["Next"]",
                            "previous": "@Localizer["Previous"]"
                        },
                        "info": "@Localizer["Showing _START_ to _END_ of _TOTAL_ entries"]",
                        "infoEmpty": "@Localizer["No data available in table"]",
                        "infoFiltered": "@Localizer["Filtered from _MAX_ total entries"]",
                        "zeroRecords": "@Localizer["No records found"]"
                    }
                });

                console.log("DataTable initialized successfully");

                // Add buttons to container
                table.buttons().container().appendTo('#example1_wrapper .col-md-6:eq(0)');

                // Apply custom styling to DataTables elements
                $('.dataTables_filter input').addClass('form-control').css({
                    'display': 'inline-block',
                    'width': 'auto',
                    'margin-left': '10px'
                });

                $('.dataTables_length select').addClass('form-control').css({
                    'display': 'inline-block',
                    'width': 'auto',
                    'margin-left': '10px',
                    'margin-right': '10px'
                });
            } catch (e) {
                console.error("Error initializing DataTable:", e);
            }
        });
    </script>
}
