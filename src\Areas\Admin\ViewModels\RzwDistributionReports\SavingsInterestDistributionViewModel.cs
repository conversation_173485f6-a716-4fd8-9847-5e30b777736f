namespace RazeWinComTr.Areas.Admin.ViewModels.RzwDistributionReports
{
    /// <summary>
    /// View model for savings interest distribution data
    /// </summary>
    public class SavingsInterestDistributionViewModel
    {
        /// <summary>
        /// Total RZW distributed through savings interest
        /// </summary>
        public decimal TotalRzwDistributed { get; set; }

        /// <summary>
        /// Number of interest payments made
        /// </summary>
        public int TotalPaymentCount { get; set; }

        /// <summary>
        /// Number of unique accounts that received interest
        /// </summary>
        public int UniqueAccountCount { get; set; }

        /// <summary>
        /// Average interest payment amount
        /// </summary>
        public decimal AveragePaymentAmount => TotalPaymentCount > 0 
            ? Math.Round(TotalRzwDistributed / TotalPaymentCount, 8) 
            : 0;

        /// <summary>
        /// Daily breakdown of savings interest payments
        /// </summary>
        public List<DailySavingsInterestViewModel> DailyBreakdown { get; set; } = new();

        /// <summary>
        /// Breakdown by term type (Daily, Weekly, Monthly, Yearly)
        /// </summary>
        public List<TermTypeBreakdownViewModel> TermTypeBreakdown { get; set; } = new();
    }

    /// <summary>
    /// Daily savings interest payment data
    /// </summary>
    public class DailySavingsInterestViewModel
    {
        /// <summary>
        /// Date of the payments
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// Total RZW distributed on this date
        /// </summary>
        public decimal TotalRzwDistributed { get; set; }

        /// <summary>
        /// Number of payments made on this date
        /// </summary>
        public int PaymentCount { get; set; }

        /// <summary>
        /// Number of unique accounts that received payments
        /// </summary>
        public int UniqueAccountCount { get; set; }
    }

    /// <summary>
    /// Term type breakdown for savings interest
    /// </summary>
    public class TermTypeBreakdownViewModel
    {
        /// <summary>
        /// Term type (Daily, Weekly, Monthly, Yearly)
        /// </summary>
        public string TermType { get; set; } = string.Empty;

        /// <summary>
        /// Total RZW distributed for this term type
        /// </summary>
        public decimal TotalRzwDistributed { get; set; }

        /// <summary>
        /// Number of payments for this term type
        /// </summary>
        public int PaymentCount { get; set; }

        /// <summary>
        /// Number of unique accounts for this term type
        /// </summary>
        public int UniqueAccountCount { get; set; }

        /// <summary>
        /// Percentage of total savings interest distribution
        /// </summary>
        public decimal Percentage { get; set; }
    }
}
