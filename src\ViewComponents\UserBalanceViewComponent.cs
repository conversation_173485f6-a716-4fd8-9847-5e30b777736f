using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Hybrid;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Models;

namespace RazeWinComTr.ViewComponents
{
    public class UserBalanceViewComponent : ViewComponent
    {
        private readonly AppDbContext _context;
        private readonly HybridCache _cache;
        private readonly IStringLocalizer<SharedResource> _localizer;
        private readonly IRzwWalletBalanceManagementService _rzwBalanceService;

        public UserBalanceViewComponent(
            AppDbContext context,
            HybridCache cache,
            IStringLocalizer<SharedResource> localizer,
            IRzwWalletBalanceManagementService rzwBalanceService)
        {
            _context = context;
            _cache = cache;
            _localizer = localizer;
            _rzwBalanceService = rzwBalanceService;
        }

        public async Task<IViewComponentResult> InvokeAsync()
        {
            var userId = HttpContext.User.GetClaimUserId();
            if (!userId.HasValue)
            {
                return View(new UserBalanceViewModel { TryBalance = 0 });
            }

            // Cache key based on user ID
            string cacheKey = $"UserBalance_{userId.Value}";

            // Try to get from cache first
            var cachedViewModel = await _cache.GetOrCreateAsync<UserBalanceViewModel>(
                cacheKey,
                async (cancel) =>
                {
                    // Get TRY balance directly from User table
                    var user = await _context.Users.FindAsync(userId.Value);
                    var tryBalance = user?.Balance ?? 0;

                    // Get RZW balance information
                    RzwBalanceInfo? rzwBalance = null;
                    try
                    {
                        rzwBalance = await _rzwBalanceService.GetRzwBalanceInfoAsync(userId.Value);
                    }
                    catch
                    {
                        // If RZW service fails, continue without RZW data
                        rzwBalance = null;
                    }

                    return new UserBalanceViewModel
                    {
                        TryBalance = tryBalance,
                        BalanceText = _localizer["TRY Balance"].Value,
                        RzwBalance = rzwBalance,
                        HasRzwBalance = rzwBalance?.HasRzwBalance ?? false
                    };
                },
                new HybridCacheEntryOptions
                {
                    Expiration = TimeSpan.FromSeconds(30)
                });

            return View(cachedViewModel);
        }
    }

    public class UserBalanceViewModel
    {
        public decimal TryBalance { get; set; }
        public string BalanceText { get; set; } = string.Empty;
        public RzwBalanceInfo? RzwBalance { get; set; }
        public bool HasRzwBalance { get; set; }

        // Computed properties for display
        public string FormattedTryBalance => TryBalance.ToString("N2", new System.Globalization.CultureInfo("en-US"));
        public string FormattedRzwBalance => RzwBalance?.TotalRzw.ToString("N8", new System.Globalization.CultureInfo("en-US")) ?? "0.00000000";
    }
}
