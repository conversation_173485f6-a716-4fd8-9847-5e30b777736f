<#
.SYNOPSIS
    Language resource file management tool for multilingual applications.

.DESCRIPTION
    This script provides functions to manage language resource files (.resx) in a multilingual application.
    It can add new language keys, verify consistency across language files, and report on the status of language files.
    The script supports any number of languages and ensures all language files contain the same keys.

.PARAMETER ResourceDir
    The directory containing the resource files. Defaults to "..\..\Areas\Admin\Resources" (relative to script location).

.PARAMETER Command
    The command to execute. Available commands:
    - add: Add a new language key to all resource files
    - add-batch: Add multiple language keys at once
    - verify: Verify that all resource files contain the same keys
    - sync: Synchronize all resource files to ensure they contain the same keys
    - report: Generate a report on the status of language files
    - sort: Sort all language keys alphabetically
    - check-key: Check if a specific language key exists in all resource files
    - check-keys: Check if multiple language keys from a file exist in all resource files
    - check-duplicates: Check for duplicate keys in resource files
    - remove-duplicates: Remove duplicate keys from resource files

.PARAMETER KeysFile
    JSON file containing language keys and values (used with 'add-batch' and 'check-keys' commands).
    Format:
    [
      {
        "key": "Welcome Message",
        "defaultValue": "Welcome",
        "values": {
          "en": "Welcome",
          "tr": "Hoş Geldiniz",
          "fr": "Bienvenue"
        },
        "comment": "Welcome message on homepage"
      },
      {
        "key": "Goodbye Message",
        "defaultValue": "Goodbye",
        "values": {
          "en": "Goodbye",
          "tr": "Hoşça Kalın",
          "fr": "Au revoir"
        },
        "comment": "Goodbye message"
      }
    ]

.PARAMETER Key
    The language key to add (used with 'add' command).

.PARAMETER DefaultValue
    The default value for the key (used with 'add' command). This will be used for all languages if no specific value is provided.

.PARAMETER Values
    A hashtable of language-specific values (used with 'add' command). Format: @{"en"="English Value"; "tr"="Turkish Value"; "fr"="French Value"}

.PARAMETER Comment
    An optional comment for the key (used with 'add' command).

.EXAMPLE
    # Add a new key using a JSON file (recommended approach)
    .\LanguageManager.ps1 -Command add -KeysFile "single_key.json"

.EXAMPLE
    # Add multiple keys from a JSON file
    .\LanguageManager.ps1 -Command add-batch -KeysFile "keys.json"

.EXAMPLE
    # Verify that all resource files contain the same keys
    .\LanguageManager.ps1 -Command verify

.EXAMPLE
    # Synchronize all resource files to ensure they contain the same keys
    .\LanguageManager.ps1 -Command sync

.EXAMPLE
    # Generate a report on the status of language files
    .\LanguageManager.ps1 -Command report

.EXAMPLE
    # Sort all language keys alphabetically
    .\LanguageManager.ps1 -Command sort

.EXAMPLE
    # Check if a specific language key exists in all resource files
    .\LanguageManager.ps1 -Command check-key -Key "Welcome Message"

.EXAMPLE
    # Check if multiple language keys from a file exist in all resource files
    .\LanguageManager.ps1 -Command check-keys -KeysFile "keys_to_check.txt"

.EXAMPLE
    # Check for duplicate keys in resource files
    .\LanguageManager.ps1 -Command check-duplicates

.EXAMPLE
    # Remove duplicate keys from resource files
    .\LanguageManager.ps1 -Command remove-duplicates

.EXAMPLE
    # Use custom resource directory
    .\LanguageManager.ps1 -ResourceDir "C:\MyProject\Resources" -Command verify
#>

param (
    [Parameter(Mandatory = $false)]
    [string]$ResourceDir = "..\..\Areas\Admin\Resources",

    [Parameter(Mandatory = $true)]
    [ValidateSet('add', 'add-batch', 'verify', 'sync', 'report', 'sort', 'check-key', 'check-keys', 'check-duplicates', 'remove-duplicates')]
    [string]$Command,

    [Parameter(Mandatory = $false)]
    [string]$KeysFile,

    [Parameter(Mandatory = $false)]
    [string]$Key,

    [Parameter(Mandatory = $false)]
    [string]$DefaultValue,

    [Parameter(Mandatory = $false)]
    [hashtable]$Values = @{},

    [Parameter(Mandatory = $false)]
    [string]$Comment
)

# Get the script directory to use as a reference point
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Write-Host "Script directory: $scriptDir"

# Resolve the resource directory path
try {
    # If ResourceDir is a relative path, resolve it relative to the script directory
    if (-not [System.IO.Path]::IsPathRooted($ResourceDir)) {
        $ResourceDir = Join-Path -Path $scriptDir -ChildPath $ResourceDir
    }

    # Ensure the path exists
    if (-not (Test-Path $ResourceDir)) {
        Write-Error "Resource directory not found: $ResourceDir"
        Write-Host "Current script directory: $scriptDir"
        exit 1
    }

    # Get the absolute path
    $ResourceDir = (Resolve-Path $ResourceDir).Path
    Write-Host "Resource directory resolved to: $ResourceDir"
} catch {
    Write-Error "Error resolving resource directory: $_"
    exit 1
}

# Function to find duplicate keys in a resource file
function Find-DuplicateKeys {
    param (
        [string]$FilePath
    )

    if (-not (Test-Path $FilePath)) {
        Write-Warning "File not found: $FilePath"
        return @()
    }

    Write-Host "Checking file: $FilePath"

    # Load the XML file
    $xml = New-Object System.Xml.XmlDocument
    $xml.Load($FilePath)

    # Get all data nodes
    $dataNodes = $xml.SelectNodes("//data")

    # Create a hashtable to store key counts
    $keyCounts = @{}

    # Count occurrences of each key
    foreach ($node in $dataNodes) {
        $key = $node.GetAttribute("name")
        if (-not $keyCounts.ContainsKey($key)) {
            $keyCounts[$key] = 0
        }
        $keyCounts[$key]++
    }

    # Find duplicate keys
    $duplicateKeys = @()
    foreach ($key in $keyCounts.Keys) {
        if ($keyCounts[$key] -gt 1) {
            $duplicateKeys += $key
        }
    }

    return $duplicateKeys
}

# Function to remove duplicate keys from a resource file
function Remove-DuplicateKeys {
    param (
        [string]$FilePath
    )

    if (-not (Test-Path $FilePath)) {
        Write-Warning "File not found: $FilePath"
        return $false
    }

    Write-Host "Processing file: $FilePath"

    # Load the XML file
    $xml = New-Object System.Xml.XmlDocument
    $xml.Load($FilePath)

    # Find the root element
    $rootElement = $xml.SelectSingleNode("//root")

    # Get all data nodes
    $dataNodes = $rootElement.SelectNodes("data")

    # Create a hashtable to store keys and their first occurrence
    $keyNodes = @{}
    $duplicatesRemoved = $false

    # Find duplicate keys
    foreach ($node in $dataNodes) {
        $key = $node.GetAttribute("name")

        if ($keyNodes.ContainsKey($key)) {
            # This is a duplicate, remove it
            $rootElement.RemoveChild($node) | Out-Null
            Write-Host "  Removed duplicate key: $key" -ForegroundColor Yellow
            $duplicatesRemoved = $true
        } else {
            # This is the first occurrence, keep it
            $keyNodes[$key] = $node
        }
    }

    if ($duplicatesRemoved) {
        # Save the XML file with UTF-8 encoding
        $xmlSettings = New-Object System.Xml.XmlWriterSettings
        $xmlSettings.Encoding = [System.Text.Encoding]::UTF8
        $xmlSettings.Indent = $true

        $xmlWriter = [System.Xml.XmlWriter]::Create($FilePath, $xmlSettings)
        $xml.Save($xmlWriter)
        $xmlWriter.Close()

        Write-Host "Saved changes to $FilePath" -ForegroundColor Green
    } else {
        Write-Host "No duplicate keys found in $FilePath" -ForegroundColor Green
    }

    return $duplicatesRemoved
}

# Function to get language code from file name
function Get-LanguageCode {
    param (
        [string]$FileName
    )

    # Default language is English
    $langCode = "en"

    # Extract language code from file name (e.g., SharedResource.tr.resx -> tr)
    if ($FileName -match "\.([a-z]{2})\.resx$") {
        $langCode = $matches[1]
    }

    return $langCode
}

# Function to check if a key exists in a resource file
function Test-KeyExists {
    param (
        [string]$FilePath,
        [string]$Key
    )

    if (-not (Test-Path $FilePath)) {
        return $false
    }

    # Use XML parsing instead of regex for more reliable matching
    try {
        $xml = New-Object System.Xml.XmlDocument
        $xml.Load($FilePath)
        $dataNode = $xml.SelectSingleNode("//data[@name='$([System.Security.SecurityElement]::Escape($Key))']")
        return ($null -ne $dataNode)
    }
    catch {
        Write-Warning "Error checking if key exists: $_"
        # Fallback to regex if XML parsing fails
        $content = Get-Content $FilePath -Raw
        $escapedKey = [regex]::Escape($Key)
        return $content -match "<data\s+name=`"$escapedKey`"\s+xml:space=`"preserve`">"
    }
}

# Function to get all keys from a resource file
function Get-ResourceKeys {
    param (
        [string]$FilePath
    )

    if (-not (Test-Path $FilePath)) {
        return @()
    }

    $xml = New-Object System.Xml.XmlDocument
    $xml.Load($FilePath)
    $dataNodes = $xml.SelectNodes("//data")

    $keys = @()
    foreach ($node in $dataNodes) {
        $keys += $node.GetAttribute("name")
    }

    return $keys
}

# Function to add a key to a resource file
function Add-LanguageKey {
    param (
        [string]$FilePath,
        [string]$Key,
        [string]$Value,
        [string]$Comment,
        [switch]$NoSort = $false
    )

    # Check if the key already exists
    if (Test-KeyExists -FilePath $FilePath -Key $Key) {
        Write-Warning "Key '$Key' already exists in $FilePath. Skipping."
        return
    }

    # Load the XML file
    $xml = New-Object System.Xml.XmlDocument
    $xml.Load($FilePath)

    # Create the new data element
    $dataElement = $xml.CreateElement("data")
    $dataElement.SetAttribute("name", $Key)
    $dataElement.SetAttribute("xml:space", "preserve")

    # Create the value element
    $valueElement = $xml.CreateElement("value")
    $valueElement.InnerText = $Value
    $dataElement.AppendChild($valueElement) | Out-Null

    # Add comment if provided
    if (-not [string]::IsNullOrEmpty($Comment)) {
        $commentElement = $xml.CreateElement("comment")
        $commentElement.InnerText = $Comment
        $dataElement.AppendChild($commentElement) | Out-Null
    }

    # Find the root element
    $rootElement = $xml.SelectSingleNode("//root")

    # Add the new data element
    $rootElement.AppendChild($dataElement) | Out-Null

    # Sort the data elements alphabetically by name attribute if requested
    if (-not $NoSort) {
        Sort-ResourceFile -FilePath $FilePath -Xml $xml
    } else {
        # Save the XML file without sorting with UTF-8 encoding
        $xmlSettings = New-Object System.Xml.XmlWriterSettings
        $xmlSettings.Encoding = [System.Text.Encoding]::UTF8
        $xmlSettings.Indent = $true

        $xmlWriter = [System.Xml.XmlWriter]::Create($FilePath, $xmlSettings)
        $xml.Save($xmlWriter)
        $xmlWriter.Close()
    }

    Write-Host "Added key '$Key' to $FilePath"
}

# Function to sort language keys in a resource file
# Note: Using Sort- verb for consistency with existing code, though it's not an approved PowerShell verb
function Sort-ResourceFile {
    param (
        [string]$FilePath,
        [System.Xml.XmlDocument]$Xml = $null
    )

    if ($null -eq $Xml) {
        # Load the XML file
        $Xml = New-Object System.Xml.XmlDocument
        $Xml.Load($FilePath)
    }

    # Find the root element
    $rootElement = $Xml.SelectSingleNode("//root")

    # Get all data nodes
    $dataNodes = $rootElement.SelectNodes("data")

    # Sort the data elements alphabetically by name attribute
    $sortedDataNodes = $dataNodes | Sort-Object { $_.GetAttribute("name") }

    # Remove all data nodes
    foreach ($node in $dataNodes) {
        $rootElement.RemoveChild($node) | Out-Null
    }

    # Add sorted data nodes back
    foreach ($node in $sortedDataNodes) {
        $rootElement.AppendChild($node) | Out-Null
    }

    # Save the XML file with UTF-8 encoding
    $xmlSettings = New-Object System.Xml.XmlWriterSettings
    $xmlSettings.Encoding = [System.Text.Encoding]::UTF8
    $xmlSettings.Indent = $true

    $xmlWriter = [System.Xml.XmlWriter]::Create($FilePath, $xmlSettings)
    $Xml.Save($xmlWriter)
    $xmlWriter.Close()

    return $true
}

# Function to get the value of a key from a resource file
function Get-KeyValue {
    param (
        [string]$FilePath,
        [string]$Key
    )

    if (-not (Test-Path $FilePath)) {
        return $null
    }

    $xml = New-Object System.Xml.XmlDocument
    $xml.Load($FilePath)
    $dataNode = $xml.SelectSingleNode("//data[@name='$Key']")

    if ($null -ne $dataNode) {
        $valueNode = $dataNode.SelectSingleNode("value")
        if ($null -ne $valueNode) {
            return $valueNode.InnerText
        }
    }

    return $null
}

# Function to get the comment of a key from a resource file
function Get-KeyComment {
    param (
        [string]$FilePath,
        [string]$Key
    )

    if (-not (Test-Path $FilePath)) {
        return $null
    }

    $xml = New-Object System.Xml.XmlDocument
    $xml.Load($FilePath)
    $dataNode = $xml.SelectSingleNode("//data[@name='$Key']")

    if ($null -ne $dataNode) {
        $commentNode = $dataNode.SelectSingleNode("comment")
        if ($null -ne $commentNode) {
            return $commentNode.InnerText
        }
    }

    return $null
}

# Function to verify that all resource files contain the same keys
function Test-ResourceConsistency {
    param (
        [string]$ResourceDir
    )

    $resourceFiles = Get-ChildItem -Path $ResourceDir -Filter "*.resx" -Recurse

    if ($resourceFiles.Count -eq 0) {
        Write-Warning "No resource files found in $ResourceDir"
        return $false
    }

    # Get all keys from the first file (reference)
    $referenceFile = $resourceFiles[0]
    $referenceKeys = Get-ResourceKeys -FilePath $referenceFile.FullName
    $referenceLanguage = Get-LanguageCode -FileName $referenceFile.Name

    Write-Host "Reference file: $($referenceFile.Name) ($referenceLanguage) with $($referenceKeys.Count) keys"

    $allConsistent = $true
    $missingKeysReport = @{}

    # Check each file against the reference
    foreach ($file in $resourceFiles) {
        if ($file.FullName -eq $referenceFile.FullName) {
            continue
        }

        $fileKeys = Get-ResourceKeys -FilePath $file.FullName
        $language = Get-LanguageCode -FileName $file.Name

        Write-Host "Checking file: $($file.Name) ($language) with $($fileKeys.Count) keys"

        # Find missing keys
        $missingKeys = $referenceKeys | Where-Object { $_ -notin $fileKeys }

        if ($missingKeys.Count -gt 0) {
            $allConsistent = $false
            $missingKeysReport[$file.Name] = $missingKeys
            Write-Warning "File $($file.Name) is missing $($missingKeys.Count) keys"
        }

        # Find extra keys
        $extraKeys = $fileKeys | Where-Object { $_ -notin $referenceKeys }

        if ($extraKeys.Count -gt 0) {
            $allConsistent = $false
            Write-Warning "File $($file.Name) has $($extraKeys.Count) extra keys"
        }
    }

    if ($allConsistent) {
        Write-Host "All resource files are consistent" -ForegroundColor Green
    } else {
        Write-Host "Resource files are not consistent" -ForegroundColor Yellow

        # Print missing keys report
        foreach ($file in $missingKeysReport.Keys) {
            Write-Host "Missing keys in ${file}:" -ForegroundColor Yellow
            foreach ($key in $missingKeysReport[$file]) {
                Write-Host "  - $key" -ForegroundColor Yellow
            }
        }
    }

    return $allConsistent
}

# Function to synchronize all resource files
function Sync-ResourceFiles {
    param (
        [string]$ResourceDir,
        [switch]$AutoTranslate = $false
    )

    $resourceFiles = Get-ChildItem -Path $ResourceDir -Filter "*.resx" -Recurse

    if ($resourceFiles.Count -eq 0) {
        Write-Warning "No resource files found in $ResourceDir"
        return
    }

    # Get all keys from all files
    $allKeys = @{}
    $fileLanguages = @{}
    $baseNameToFiles = @{}
    $languageGroups = @{}

    foreach ($file in $resourceFiles) {
        $language = Get-LanguageCode -FileName $file.Name
        $fileLanguages[$file.FullName] = $language

        # Group files by base name (e.g., SharedResource)
        $baseName = $file.Name -replace "\.[a-z]{2}\.resx$", ".resx"
        $baseName = $baseName -replace "\.resx$", ""

        if (-not $baseNameToFiles.ContainsKey($baseName)) {
            $baseNameToFiles[$baseName] = @()
        }
        $baseNameToFiles[$baseName] += $file

        # Group languages
        if (-not $languageGroups.ContainsKey($baseName)) {
            $languageGroups[$baseName] = @{}
        }
        $languageGroups[$baseName][$language] = $file.FullName

        $fileKeys = Get-ResourceKeys -FilePath $file.FullName
        foreach ($key in $fileKeys) {
            if (-not $allKeys.ContainsKey($key)) {
                $allKeys[$key] = @{}
            }

            $value = Get-KeyValue -FilePath $file.FullName -Key $key
            $allKeys[$key][$language] = $value
        }
    }

    Write-Host "Found $($allKeys.Count) unique keys across all resource files"

    # Process each base name group separately
    foreach ($baseName in $baseNameToFiles.Keys) {
        Write-Host "Processing resource group: $baseName" -ForegroundColor Cyan
        $languages = $languageGroups[$baseName].Keys

        # Find the reference language (prefer English, then first available)
        $referenceLanguage = "en"
        if (-not $languages.Contains("en")) {
            $referenceLanguage = $languages | Select-Object -First 1
        }

        Write-Host "Using $referenceLanguage as reference language for $baseName"

        # Add missing keys to each file in this group
        foreach ($language in $languages) {
            $filePath = $languageGroups[$baseName][$language]
            $fileKeys = Get-ResourceKeys -FilePath $filePath
            $missingCount = 0

            foreach ($key in $allKeys.Keys) {
                if ($key -notin $fileKeys) {
                    # Determine the value to use
                    $value = $null
                    $comment = "Auto-synchronized key"

                    # Strategy 1: Try to use the value from the same language in another file group
                    if ($null -eq $value) {
                        foreach ($otherBaseName in $baseNameToFiles.Keys) {
                            if ($otherBaseName -ne $baseName -and
                                $languageGroups[$otherBaseName].ContainsKey($language) -and
                                $allKeys[$key].ContainsKey($language)) {
                                $value = $allKeys[$key][$language]
                                $comment = "Auto-synchronized from another file group"
                                break
                            }
                        }
                    }

                    # Strategy 2: Try to use the value from the reference language in this file group
                    if ($null -eq $value -and $allKeys[$key].ContainsKey($referenceLanguage)) {
                        $refValue = $allKeys[$key][$referenceLanguage]

                        # If the language is not the reference language, mark it as needing translation
                        if ($language -ne $referenceLanguage) {
                            $value = $refValue
                            $comment = "Needs translation from $referenceLanguage"
                        } else {
                            $value = $refValue
                        }
                    }

                    # Strategy 3: Use any available value from any language
                    if ($null -eq $value -and $allKeys[$key].Count -gt 0) {
                        $firstLang = $allKeys[$key].Keys | Select-Object -First 1
                        $value = $allKeys[$key][$firstLang]
                        $comment = "Needs translation from $firstLang"
                    }

                    # Strategy 4: Last resort - use a placeholder indicating translation is needed
                    if ($null -eq $value) {
                        $value = "[TRANSLATION NEEDED]"
                        $comment = "Translation needed for key: $key"
                    }

                    Add-LanguageKey -FilePath $filePath -Key $key -Value $value -Comment $comment -NoSort
                    $missingCount++
                }
            }

            if ($missingCount -gt 0) {
                Write-Host "Added $missingCount missing keys to $language resource file in $baseName" -ForegroundColor Yellow
            } else {
                Write-Host "No missing keys in $language resource file in $baseName" -ForegroundColor Green
            }
        }
    }

    Write-Host "All resource files have been synchronized" -ForegroundColor Green
    Write-Host "Note: Keys marked with [TRANSLATION NEEDED] or 'Needs translation' comments should be reviewed and properly translated" -ForegroundColor Yellow
}

# Function to generate a report on the status of language files
function Get-ResourceReport {
    param (
        [string]$ResourceDir
    )

    $resourceFiles = Get-ChildItem -Path $ResourceDir -Filter "*.resx" -Recurse

    if ($resourceFiles.Count -eq 0) {
        Write-Warning "No resource files found in $ResourceDir"
        return
    }

    $report = @{
        TotalFiles = $resourceFiles.Count
        Languages = @{}
        TotalUniqueKeys = 0
        KeysPerLanguage = @{}
        MissingKeys = @{}
    }

    # Get all keys from all files
    $allKeys = @{}

    foreach ($file in $resourceFiles) {
        $language = Get-LanguageCode -FileName $file.Name

        if (-not $report.Languages.ContainsKey($language)) {
            $report.Languages[$language] = 0
            $report.KeysPerLanguage[$language] = 0
            $report.MissingKeys[$language] = @()
        }

        $report.Languages[$language]++

        $fileKeys = Get-ResourceKeys -FilePath $file.FullName
        $report.KeysPerLanguage[$language] += $fileKeys.Count

        foreach ($key in $fileKeys) {
            if (-not $allKeys.ContainsKey($key)) {
                $allKeys[$key] = @()
            }

            $allKeys[$key] += $language
        }
    }

    $report.TotalUniqueKeys = $allKeys.Count

    # Find missing keys for each language
    foreach ($key in $allKeys.Keys) {
        foreach ($language in $report.Languages.Keys) {
            if ($language -notin $allKeys[$key]) {
                $report.MissingKeys[$language] += $key
            }
        }
    }

    # Print report
    Write-Host "Resource Files Report" -ForegroundColor Cyan
    Write-Host "--------------------" -ForegroundColor Cyan
    Write-Host "Total resource files: $($report.TotalFiles)" -ForegroundColor Cyan
    Write-Host "Total unique keys: $($report.TotalUniqueKeys)" -ForegroundColor Cyan
    Write-Host "Languages:" -ForegroundColor Cyan

    foreach ($language in $report.Languages.Keys) {
        Write-Host "  - ${language}: $($report.Languages[$language]) files, $($report.KeysPerLanguage[$language]) keys" -ForegroundColor Cyan

        if ($report.MissingKeys[$language].Count -gt 0) {
            Write-Host "    Missing $($report.MissingKeys[$language].Count) keys" -ForegroundColor Yellow
        } else {
            Write-Host "    No missing keys" -ForegroundColor Green
        }
    }

    # Return the report object for further processing
    return $report
}

# Function to export all language keys to a JSON file
function Export-LanguageKeys {
    param (
        [string]$ResourceDir,
        [string]$OutputFile
    )

    $resourceFiles = Get-ChildItem -Path $ResourceDir -Filter "*.resx" -Recurse

    if ($resourceFiles.Count -eq 0) {
        Write-Warning "No resource files found in $ResourceDir"
        return $false
    }

    # Get all keys and their values
    $allKeys = @{}
    $languages = @{}

    foreach ($file in $resourceFiles) {
        $language = Get-LanguageCode -FileName $file.Name
        $languages[$language] = $true

        $fileKeys = Get-ResourceKeys -FilePath $file.FullName
        foreach ($key in $fileKeys) {
            if (-not $allKeys.ContainsKey($key)) {
                $allKeys[$key] = @{
                    values = @{}
                    comment = $null
                }
            }

            $value = Get-KeyValue -FilePath $file.FullName -Key $key
            $allKeys[$key].values[$language] = $value

            # Get comment (only need to do this once per key)
            if ($null -eq $allKeys[$key].comment) {
                $allKeys[$key].comment = Get-KeyComment -FilePath $file.FullName -Key $key
            }
        }
    }

    # Convert to array of objects for JSON export
    $keysArray = @()
    foreach ($key in $allKeys.Keys) {
        $keyObj = @{
            key = $key
            defaultValue = ""
            values = $allKeys[$key].values
            comment = $allKeys[$key].comment
        }

        # Set default value (prefer English, then first available)
        if ($allKeys[$key].values.ContainsKey("en")) {
            $keyObj.defaultValue = $allKeys[$key].values["en"]
        } else {
            $firstLang = $allKeys[$key].values.Keys | Select-Object -First 1
            if ($null -ne $firstLang) {
                $keyObj.defaultValue = $allKeys[$key].values[$firstLang]
            }
        }

        $keysArray += $keyObj
    }

    # Sort by key
    $keysArray = $keysArray | Sort-Object -Property key

    # Export to JSON
    $keysArray | ConvertTo-Json -Depth 10 | Out-File -FilePath $OutputFile -Encoding UTF8

    Write-Host "Exported $($keysArray.Count) keys to $OutputFile" -ForegroundColor Green
    return $true
}

# Function to add multiple language keys from a JSON file
function Add-LanguageKeysFromJson {
    param (
        [string]$ResourceDir,
        [string]$JsonFile
    )

    if (-not (Test-Path $JsonFile)) {
        Write-Error "JSON file not found: $JsonFile"
        return $false
    }

    try {
        $keysArray = Get-Content -Path $JsonFile -Raw -Encoding UTF8 | ConvertFrom-Json
    } catch {
        Write-Error "Failed to parse JSON file: $_"
        return $false
    }

    $resourceFiles = Get-ChildItem -Path $ResourceDir -Filter "*.resx" -Recurse

    if ($resourceFiles.Count -eq 0) {
        Write-Warning "No resource files found in $ResourceDir"
        return $false
    }

    $addedCount = 0
    $skippedCount = 0

    foreach ($keyObj in $keysArray) {
        $key = $keyObj.key
        $defaultValue = $keyObj.defaultValue
        $values = $keyObj.values
        $comment = $keyObj.comment

        if ([string]::IsNullOrEmpty($key)) {
            Write-Warning "Skipping entry with empty key"
            $skippedCount++
            continue
        }

        if ([string]::IsNullOrEmpty($defaultValue) -and ($null -eq $values -or $values.Count -eq 0)) {
            Write-Warning "Skipping key '$key' because no values were provided"
            $skippedCount++
            continue
        }

        $keyAddedToAnyFile = $false

        foreach ($file in $resourceFiles) {
            $filePath = $file.FullName
            $language = Get-LanguageCode -FileName $file.Name

            # Skip if key already exists in this file
            if (Test-KeyExists -FilePath $filePath -Key $key) {
                continue
            }

            # Determine which value to use
            $value = $defaultValue

            if ($null -ne $values -and $values.PSObject.Properties.Name -contains $language) {
                $value = $values.$language
            }

            Add-LanguageKey -FilePath $filePath -Key $key -Value $value -Comment $comment -NoSort
            $keyAddedToAnyFile = $true
        }

        if ($keyAddedToAnyFile) {
            $addedCount++
        }
    }

    Write-Host "Added $addedCount language keys to resource files. Skipped $skippedCount keys." -ForegroundColor Green
    return $true
}

# Function to sort all resource files
# Note: Using Sort- verb for consistency with existing code, though it's not an approved PowerShell verb
function Sort-AllResourceFiles {
    param (
        [string]$ResourceDir
    )

    $resourceFiles = Get-ChildItem -Path $ResourceDir -Filter "*.resx" -Recurse

    if ($resourceFiles.Count -eq 0) {
        Write-Warning "No resource files found in $ResourceDir"
        return $false
    }

    $sortedCount = 0

    foreach ($file in $resourceFiles) {
        $filePath = $file.FullName
        $result = Sort-ResourceFile -FilePath $filePath
        if ($result) {
            $sortedCount++
        }
    }

    Write-Host "Sorted $sortedCount resource files." -ForegroundColor Green
    return $true
}

# Function to check if a language key exists in all resource files
function Test-LanguageKeyExists {
    param (
        [string]$ResourceDir,
        [string]$Key
    )

    $resourceFiles = Get-ChildItem -Path $ResourceDir -Filter '*.resx' -Recurse

    if ($resourceFiles.Count -eq 0) {
        Write-Warning 'No resource files found in the resource directory'
        return $false
    }

    Write-Host 'Checking if key exists in all resource files...' -ForegroundColor Cyan

    $existsInAll = $true
    $missingInFiles = @()
    $existsInFiles = @()

    foreach ($file in $resourceFiles) {
        $filePath = $file.FullName
        $language = Get-LanguageCode -FileName $file.Name

        if (Test-KeyExists -FilePath $filePath -Key $Key) {
            Write-Host ('  [OK] Key exists in ' + $file.Name + ' (' + $language + ')') -ForegroundColor Green
            $existsInFiles += ($file.Name + ' (' + $language + ')')
        } else {
            Write-Host ('  [MISSING] Key does NOT exist in ' + $file.Name + ' (' + $language + ')') -ForegroundColor Red
            $existsInAll = $false
            $missingInFiles += ($file.Name + ' (' + $language + ')')
        }
    }

    if ($existsInAll) {
        Write-Host 'Key exists in all resource files.' -ForegroundColor Green
    } else {
        Write-Host 'Key is missing in some resource files:' -ForegroundColor Yellow
        foreach ($file in $missingInFiles) {
            Write-Host ('  - ' + $file) -ForegroundColor Yellow
        }

        if ($existsInFiles.Count -gt 0) {
            Write-Host 'Key exists in these resource files:' -ForegroundColor Cyan
            foreach ($file in $existsInFiles) {
                Write-Host ('  - ' + $file) -ForegroundColor Cyan
            }
        } else {
            Write-Host 'Key does not exist in any resource file.' -ForegroundColor Red
        }
    }

    return $existsInAll
}

# Function to check if multiple language keys exist in all resource files
function Test-LanguageKeysExist {
    param (
        [string]$ResourceDir,
        [string]$KeysFile
    )

    if (-not (Test-Path $KeysFile)) {
        Write-Error ('Keys file not found: ' + $KeysFile)
        return $false
    }

    try {
        # Try to read the file as a simple text file with one key per line
        $keys = Get-Content -Path $KeysFile -Encoding UTF8
        $isSimpleTextFile = $true
    } catch {
        $isSimpleTextFile = $false
    }

    if (-not $isSimpleTextFile) {
        try {
            # Try to read the file as a JSON file
            $keysArray = Get-Content -Path $KeysFile -Raw -Encoding UTF8 | ConvertFrom-Json
            $keys = @()
            foreach ($keyObj in $keysArray) {
                if ($keyObj.PSObject.Properties.Name -contains 'key') {
                    $keys += $keyObj.key
                }
            }
        } catch {
            Write-Error ('Failed to parse keys file: ' + $_)
            return $false
        }
    }

    $resourceFiles = Get-ChildItem -Path $ResourceDir -Filter '*.resx' -Recurse

    if ($resourceFiles.Count -eq 0) {
        Write-Warning 'No resource files found in the resource directory'
        return $false
    }

    Write-Host ('Checking if ' + $keys.Count + ' keys exist in all resource files...') -ForegroundColor Cyan

    $missingKeys = @()
    $existingKeys = @()
    $keysToAdd = @()

    foreach ($key in $keys) {
        # Skip empty lines or comments
        if ([string]::IsNullOrWhiteSpace($key) -or $key.Trim().StartsWith('#')) {
            continue
        }

        $key = $key.Trim()
        Write-Host ('Checking key: ' + $key) -ForegroundColor Cyan

        $existsInAll = $true
        $missingInFiles = @()

        foreach ($file in $resourceFiles) {
            $filePath = $file.FullName
            $language = Get-LanguageCode -FileName $file.Name

            if (-not (Test-KeyExists -FilePath $filePath -Key $key)) {
                $existsInAll = $false
                $missingInFiles += ($file.Name + ' (' + $language + ')')
            }
        }

        if ($existsInAll) {
            Write-Host ('  [OK] Key ''' + $key + ''' exists in all resource files.') -ForegroundColor Green
            $existingKeys += $key
        } else {
            Write-Host ('  [MISSING] Key ''' + $key + ''' is missing in some resource files:') -ForegroundColor Yellow
            foreach ($file in $missingInFiles) {
                Write-Host ('    - ' + $file) -ForegroundColor Yellow
            }
            $missingKeys += $key
            $keysToAdd += $key
        }
    }

    Write-Host 'Summary:' -ForegroundColor Cyan
    Write-Host ('  Total keys checked: ' + $keys.Count) -ForegroundColor Cyan
    Write-Host ('  Keys existing in all resource files: ' + $existingKeys.Count) -ForegroundColor Green
    Write-Host ('  Keys missing in some resource files: ' + $missingKeys.Count) -ForegroundColor Yellow

    if ($missingKeys.Count -gt 0) {
        Write-Host 'Missing keys:' -ForegroundColor Yellow
        foreach ($key in $missingKeys) {
            Write-Host ('  - ' + $key) -ForegroundColor Yellow
        }

        Write-Host 'To add these keys, use the add command:' -ForegroundColor Cyan
        Write-Host '.\LanguageManager.ps1 -Command add -Key "Key Name" -Values @{"en"="English Value"; "tr"="Turkish Value"} -Comment "Comment"' -ForegroundColor Cyan
    }

    return ($missingKeys.Count -eq 0)
}

# Find all resource files
$resourceFiles = Get-ChildItem -Path $ResourceDir -Filter '*.resx' -Recurse

# Execute the requested command
switch ($Command) {
    'add' {
        if ([string]::IsNullOrEmpty($Key)) {
            Write-Error 'Key parameter is required for add command'
            exit 1
        }

        if ([string]::IsNullOrEmpty($KeysFile)) {
            Write-Error 'KeysFile parameter is required for add command. Please provide a JSON file with language-specific values.'
            Write-Host 'Example JSON format:' -ForegroundColor Yellow
            Write-Host '[' -ForegroundColor Gray
            Write-Host '  {' -ForegroundColor Gray
            Write-Host '    "key": "Your Key Name",' -ForegroundColor Gray
            Write-Host '    "values": {' -ForegroundColor Gray
            Write-Host '      "en": "English Value",' -ForegroundColor Gray
            Write-Host '      "tr": "Turkish Value"' -ForegroundColor Gray
            Write-Host '    },' -ForegroundColor Gray
            Write-Host '    "comment": "Optional comment"' -ForegroundColor Gray
            Write-Host '  }' -ForegroundColor Gray
            Write-Host ']' -ForegroundColor Gray
            exit 1
        }

        Add-LanguageKeysFromJson -ResourceDir $ResourceDir -JsonFile $KeysFile
    }

    'add-batch' {
        if ([string]::IsNullOrEmpty($KeysFile)) {
            Write-Error 'KeysFile parameter is required for add-batch command'
            exit 1
        }

        Add-LanguageKeysFromJson -ResourceDir $ResourceDir -JsonFile $KeysFile
    }

    'verify' {
        Test-ResourceConsistency -ResourceDir $ResourceDir
    }

    'sync' {
        Sync-ResourceFiles -ResourceDir $ResourceDir
    }

    'report' {
        Get-ResourceReport -ResourceDir $ResourceDir
    }

    'sort' {
        Sort-AllResourceFiles -ResourceDir $ResourceDir
    }

    'check-key' {
        if ([string]::IsNullOrEmpty($Key)) {
            Write-Error 'Key parameter is required for check-key command'
            exit 1
        }

        Test-LanguageKeyExists -ResourceDir $ResourceDir -Key $Key
    }

    'check-keys' {
        if ([string]::IsNullOrEmpty($KeysFile)) {
            Write-Error 'KeysFile parameter is required for check-keys command'
            exit 1
        }

        Test-LanguageKeysExist -ResourceDir $ResourceDir -KeysFile $KeysFile
    }

    'check-duplicates' {
        $hasDuplicates = $false

        foreach ($file in $resourceFiles) {
            $duplicateKeys = Find-DuplicateKeys -FilePath $file.FullName

            if ($duplicateKeys.Count -gt 0) {
                $hasDuplicates = $true
                Write-Host "Found $($duplicateKeys.Count) duplicate keys in $($file.Name):" -ForegroundColor Yellow
                foreach ($key in $duplicateKeys) {
                    Write-Host "  - $key" -ForegroundColor Yellow
                }
            } else {
                Write-Host "No duplicate keys found in $($file.Name)" -ForegroundColor Green
            }
        }

        if (-not $hasDuplicates) {
            Write-Host "No duplicate keys found in any resource file" -ForegroundColor Green
        }

        return $hasDuplicates
    }

    'remove-duplicates' {
        $changesApplied = $false

        foreach ($file in $resourceFiles) {
            $result = Remove-DuplicateKeys -FilePath $file.FullName
            if ($result) {
                $changesApplied = $true
            }
        }

        if ($changesApplied) {
            Write-Host "Duplicate keys have been removed from resource files" -ForegroundColor Green
        } else {
            Write-Host "No duplicate keys found in any resource file" -ForegroundColor Green
        }

        return $changesApplied
    }
}
