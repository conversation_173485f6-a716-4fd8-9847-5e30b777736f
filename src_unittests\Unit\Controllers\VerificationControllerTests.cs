using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using RazeWinComTr.Areas.Admin.Enums;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Controllers;
using RazeWinComTr.ViewModels;

namespace RazeWinComTr.Tests.Unit.Controllers;

public class VerificationControllerTests
{
    private readonly Mock<IVerificationService> _mockVerificationService;
    private readonly Mock<IStringLocalizer<SharedResource>> _mockLocalizer;
    private readonly Mock<ILogger<VerificationController>> _mockLogger;
    private readonly VerificationController _controller;

    public VerificationControllerTests()
    {
        _mockVerificationService = new Mock<IVerificationService>();
        _mockLocalizer = new Mock<IStringLocalizer<SharedResource>>();
        _mockLogger = new Mock<ILogger<VerificationController>>();

        _controller = new VerificationController(
            _mockVerificationService.Object,
            _mockLocalizer.Object,
            _mockLogger.Object);

        SetupMockLocalizer();
    }

    private void SetupMockLocalizer()
    {
        _mockLocalizer.Setup(l => l["Verification Failed"])
            .Returns(new LocalizedString("Verification Failed", "Verification Failed"));
        _mockLocalizer.Setup(l => l["Invalid verification link. Please check your email and try again."])
            .Returns(new LocalizedString("Invalid verification link. Please check your email and try again.", "Invalid verification link. Please check your email and try again."));
        _mockLocalizer.Setup(l => l["Email Verified Successfully"])
            .Returns(new LocalizedString("Email Verified Successfully", "Email Verified Successfully"));
        _mockLocalizer.Setup(l => l["Your email address has been verified successfully. You can now access all features of your account."])
            .Returns(new LocalizedString("Your email address has been verified successfully. You can now access all features of your account.", "Your email address has been verified successfully. You can now access all features of your account."));
        _mockLocalizer.Setup(l => l["Email Already Verified"])
            .Returns(new LocalizedString("Email Already Verified", "Email Already Verified"));
        _mockLocalizer.Setup(l => l["Your email address has already been verified. No further action is required."])
            .Returns(new LocalizedString("Your email address has already been verified. No further action is required.", "Your email address has already been verified. No further action is required."));
        _mockLocalizer.Setup(l => l["Email verification failed. The link may have expired or is invalid."])
            .Returns(new LocalizedString("Email verification failed. The link may have expired or is invalid.", "Email verification failed. The link may have expired or is invalid."));
        _mockLocalizer.Setup(l => l["Verification Error"])
            .Returns(new LocalizedString("Verification Error", "Verification Error"));
        _mockLocalizer.Setup(l => l["An error occurred during verification. Please try again later."])
            .Returns(new LocalizedString("An error occurred during verification. Please try again later.", "An error occurred during verification. Please try again later."));
    }

    [Fact]
    public async Task Verify_WithEmptyToken_ShouldReturnErrorView()
    {
        // Act
        var result = await _controller.Verify(string.Empty, "Email");

        // Assert
        var viewResult = Assert.IsType<ViewResult>(result);
        Assert.Equal("VerificationResult", viewResult.ViewName);
        
        var model = Assert.IsType<VerificationResultViewModel>(viewResult.Model);
        Assert.False(model.IsSuccess);
        Assert.Equal("Verification Failed", model.Title);
        Assert.Equal("Invalid verification link. Please check your email and try again.", model.Message);
    }

    [Fact]
    public async Task Verify_WithNullToken_ShouldReturnErrorView()
    {
        // Act
        var result = await _controller.Verify(null!, "Email");

        // Assert
        var viewResult = Assert.IsType<ViewResult>(result);
        Assert.Equal("VerificationResult", viewResult.ViewName);
        
        var model = Assert.IsType<VerificationResultViewModel>(viewResult.Model);
        Assert.False(model.IsSuccess);
        Assert.Equal("Verification Failed", model.Title);
    }

    [Fact]
    public async Task Verify_WithValidToken_ShouldReturnSuccessView()
    {
        // Arrange
        string validToken = "valid-token-123";
        
        _mockVerificationService.Setup(v => v.IsTokenAlreadyVerifiedAsync(validToken))
            .ReturnsAsync(false);
        _mockVerificationService.Setup(v => v.VerifyTokenAsync(validToken))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.Verify(validToken, "Email");

        // Assert
        var viewResult = Assert.IsType<ViewResult>(result);
        Assert.Equal("VerificationResult", viewResult.ViewName);
        
        var model = Assert.IsType<VerificationResultViewModel>(viewResult.Model);
        Assert.True(model.IsSuccess);
        Assert.Equal("Email Verified Successfully", model.Title);
        Assert.Equal("Your email address has been verified successfully. You can now access all features of your account.", model.Message);
        Assert.Equal(VerificationType.Email, model.VerificationType);
    }

    [Fact]
    public async Task Verify_WithAlreadyVerifiedToken_ShouldReturnAlreadyVerifiedView()
    {
        // Arrange
        string alreadyVerifiedToken = "already-verified-token";
        
        _mockVerificationService.Setup(v => v.IsTokenAlreadyVerifiedAsync(alreadyVerifiedToken))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.Verify(alreadyVerifiedToken, "Email");

        // Assert
        var viewResult = Assert.IsType<ViewResult>(result);
        Assert.Equal("VerificationResult", viewResult.ViewName);
        
        var model = Assert.IsType<VerificationResultViewModel>(viewResult.Model);
        Assert.True(model.IsSuccess);
        Assert.Equal("Email Already Verified", model.Title);
        Assert.Equal("Your email address has already been verified. No further action is required.", model.Message);
        Assert.Equal(VerificationType.Email, model.VerificationType);
    }

    [Fact]
    public async Task Verify_WithInvalidToken_ShouldReturnErrorView()
    {
        // Arrange
        string invalidToken = "invalid-token-123";
        
        _mockVerificationService.Setup(v => v.IsTokenAlreadyVerifiedAsync(invalidToken))
            .ReturnsAsync(false);
        _mockVerificationService.Setup(v => v.VerifyTokenAsync(invalidToken))
            .ReturnsAsync(false);

        // Act
        var result = await _controller.Verify(invalidToken, "Email");

        // Assert
        var viewResult = Assert.IsType<ViewResult>(result);
        Assert.Equal("VerificationResult", viewResult.ViewName);
        
        var model = Assert.IsType<VerificationResultViewModel>(viewResult.Model);
        Assert.False(model.IsSuccess);
        Assert.Equal("Verification Failed", model.Title);
        Assert.Equal("Email verification failed. The link may have expired or is invalid.", model.Message);
    }

    [Fact]
    public async Task Verify_WithException_ShouldReturnErrorView()
    {
        // Arrange
        string tokenCausingException = "exception-token";
        
        _mockVerificationService.Setup(v => v.IsTokenAlreadyVerifiedAsync(tokenCausingException))
            .ThrowsAsync(new Exception("Database error"));

        // Act
        var result = await _controller.Verify(tokenCausingException, "Email");

        // Assert
        var viewResult = Assert.IsType<ViewResult>(result);
        Assert.Equal("VerificationResult", viewResult.ViewName);
        
        var model = Assert.IsType<VerificationResultViewModel>(viewResult.Model);
        Assert.False(model.IsSuccess);
        Assert.Equal("Verification Error", model.Title);
    }

    [Theory]
    [InlineData("Email", VerificationType.Email)]
    [InlineData("Phone", VerificationType.Phone)]
    [InlineData("Address", VerificationType.Address)]
    [InlineData("Identity", VerificationType.Identity)]
    [InlineData("BankAccount", VerificationType.BankAccount)]
    [InlineData("TwoFactor", VerificationType.TwoFactor)]
    [InlineData("InvalidType", VerificationType.Email)] // Should default to Email
    public async Task Verify_WithDifferentVerificationTypes_ShouldSetCorrectType(string typeString, VerificationType expectedType)
    {
        // Arrange
        string validToken = "valid-token-123";
        
        _mockVerificationService.Setup(v => v.IsTokenAlreadyVerifiedAsync(validToken))
            .ReturnsAsync(false);
        _mockVerificationService.Setup(v => v.VerifyTokenAsync(validToken))
            .ReturnsAsync(true);

        // Act
        var result = await _controller.Verify(validToken, typeString);

        // Assert
        var viewResult = Assert.IsType<ViewResult>(result);
        var model = Assert.IsType<VerificationResultViewModel>(viewResult.Model);
        Assert.Equal(expectedType, model.VerificationType);
    }

    [Fact]
    public async Task Verify_ShouldLogVerificationAttempts()
    {
        // Arrange
        string token = "test-token";
        
        _mockVerificationService.Setup(v => v.IsTokenAlreadyVerifiedAsync(token))
            .ReturnsAsync(false);
        _mockVerificationService.Setup(v => v.VerifyTokenAsync(token))
            .ReturnsAsync(true);

        // Act
        await _controller.Verify(token, "Email");

        // Assert
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Verification successful")),
                It.IsAny<Exception?>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task Verify_WithFailedVerification_ShouldLogWarning()
    {
        // Arrange
        string token = "failed-token";
        
        _mockVerificationService.Setup(v => v.IsTokenAlreadyVerifiedAsync(token))
            .ReturnsAsync(false);
        _mockVerificationService.Setup(v => v.VerifyTokenAsync(token))
            .ReturnsAsync(false);

        // Act
        await _controller.Verify(token, "Email");

        // Assert
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Warning,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Verification failed")),
                It.IsAny<Exception?>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }
}
