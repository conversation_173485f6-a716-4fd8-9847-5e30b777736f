using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Models;

namespace RazeWinComTr.Areas.MyAccount.Pages.RzwSavings;

public class HistoryModel : PageModel
{
    private readonly IRzwSavingsService _rzwSavingsService;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public HistoryModel(
        IRzwSavingsService rzwSavingsService,
        IStringLocalizer<SharedResource> localizer)
    {
        _rzwSavingsService = rzwSavingsService;
        _localizer = localizer;
    }

    public List<RzwSavingsAccountHistoryDisplayModel> AllAccounts { get; set; } = new();
    public RzwSavingsHistorySummary Summary { get; set; } = new();

    // Filter properties
    [BindProperty(SupportsGet = true)]
    public string? StatusFilter { get; set; }

    [BindProperty(SupportsGet = true)]
    public DateTime? FromDate { get; set; }

    [BindProperty(SupportsGet = true)]
    public DateTime? ToDate { get; set; }

    // Pagination
    [BindProperty(SupportsGet = true)]
    public int PageNumber { get; set; } = 1;

    public int PageSize { get; } = 20;
    public int TotalPages { get; set; }
    public bool HasPreviousPage => PageNumber > 1;
    public bool HasNextPage => PageNumber < TotalPages;

    public async Task<IActionResult> OnGetAsync()
    {
        var userId = User.GetClaimUserId();
        if (!userId.HasValue)
        {
            return Unauthorized();
        }

        try
        {
            // Get all user's savings accounts
            var allSavingsAccounts = await _rzwSavingsService.GetUserAllSavingsAsync(userId.Value);

            // Apply filters
            var filteredAccounts = ApplyFilters(allSavingsAccounts);

            // Calculate pagination
            var totalCount = filteredAccounts.Count;
            TotalPages = (int)Math.Ceiling(totalCount / (double)PageSize);

            // Apply pagination
            var paginatedAccounts = filteredAccounts
                .Skip((PageNumber - 1) * PageSize)
                .Take(PageSize)
                .ToList();

            // Convert to display models
            AllAccounts = paginatedAccounts.Select(account => new RzwSavingsAccountHistoryDisplayModel
            {
                Id = account.Id,
                PlanName = account.Plan?.Name ?? string.Empty,
                RzwAmount = account.RzwAmount,
                MaturityDate = account.MaturityDate,
                StartDate = account.StartDate,
                Plan = account.Plan,
                IsMatured = account.IsMaturedForDisplay,
                DaysRemaining = account.DaysRemainingForDisplay,
                TotalDays = account.TotalDaysForDisplay,
                ProgressPercentage = account.ProgressPercentageForDisplay,
                EarlyWithdrawalPenalty = account.EarlyWithdrawalPenalty,
                TotalEarnedRzw = account.TotalEarnedRzw,
                TermType = account.TermType,
                TermDuration = account.TermDuration,
                InterestRate = account.InterestRate,
                PlanDescription = account.Plan?.Description ?? string.Empty,
                Status = account.Status,
                // Generate localized term display text
                TermDisplayText = RzwSavingsDisplayHelper.GetLocalizedTermDisplayText(_localizer, account.TermType, account.TermDuration)
            }).ToList();

            // Calculate summary
            CalculateSummary(allSavingsAccounts);
        }
        catch (Exception)
        {
            AllAccounts = new List<RzwSavingsAccountHistoryDisplayModel>();
            Summary = new RzwSavingsHistorySummary();
        }

        return Page();
    }

    private List<RzwSavingsAccount> ApplyFilters(List<RzwSavingsAccount> accounts)
    {
        var filtered = accounts.AsQueryable();

        // Status filter
        if (!string.IsNullOrEmpty(StatusFilter))
        {
            filtered = filtered.Where(a => a.Status == StatusFilter);
        }

        // Date range filter
        if (FromDate.HasValue)
        {
            filtered = filtered.Where(a => a.StartDate.Date >= FromDate.Value.Date);
        }

        if (ToDate.HasValue)
        {
            filtered = filtered.Where(a => a.StartDate.Date <= ToDate.Value.Date);
        }

        return filtered.OrderByDescending(a => a.CreatedDate).ToList();
    }

    private void CalculateSummary(List<RzwSavingsAccount> accounts)
    {
        Summary = new RzwSavingsHistorySummary
        {
            TotalAccounts = accounts.Count,
            ActiveAccounts = accounts.Count(a => a.Status == RzwSavingsStatus.Active),
            MaturedAccounts = accounts.Count(a => a.Status == RzwSavingsStatus.Matured),
            WithdrawnAccounts = accounts.Count(a => a.Status == RzwSavingsStatus.Withdrawn),
            CancelledAccounts = accounts.Count(a => a.Status == RzwSavingsStatus.Cancelled),
            TotalInvested = accounts.Sum(a => a.RzwAmount),
            TotalEarned = accounts.Sum(a => a.TotalEarnedRzw),
            CurrentlyLocked = accounts.Where(a => a.Status == RzwSavingsStatus.Active).Sum(a => a.RzwAmount)
        };
    }
}

/// <summary>
/// Display model for RZW Savings Account with additional status information for history view
/// </summary>
public class RzwSavingsAccountHistoryDisplayModel
{
    public int Id { get; set; }
    public string PlanName { get; set; } = string.Empty;
    public decimal RzwAmount { get; set; }
    public DateTime MaturityDate { get; set; }
    public DateTime StartDate { get; set; }
    public RzwSavingsPlan? Plan { get; set; }
    public bool IsMatured { get; set; }
    public int DaysRemaining { get; set; }
    public int TotalDays { get; set; }
    public double ProgressPercentage { get; set; }
    public decimal EarlyWithdrawalPenalty { get; set; }
    public decimal TotalEarnedRzw { get; set; }
    public string TermType { get; set; } = string.Empty;
    public int TermDuration { get; set; }
    public decimal InterestRate { get; set; }
    public string PlanDescription { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string TermDisplayText { get; set; } = string.Empty;

    // Computed display properties
    public string EarlyWithdrawalPenaltyDisplayText => $"{(EarlyWithdrawalPenalty * 100).ToString("N1")}%";
    public string StatusBadgeClass => Status switch
    {
        RzwSavingsStatus.Active => "badge-success",
        RzwSavingsStatus.Matured => "badge-info",
        RzwSavingsStatus.Withdrawn => "badge-warning",
        RzwSavingsStatus.Cancelled => "badge-danger",
        _ => "badge-secondary"
    };
}

/// <summary>
/// Summary information for savings history
/// </summary>
public class RzwSavingsHistorySummary
{
    public int TotalAccounts { get; set; }
    public int ActiveAccounts { get; set; }
    public int MaturedAccounts { get; set; }
    public int WithdrawnAccounts { get; set; }
    public int CancelledAccounts { get; set; }
    public decimal TotalInvested { get; set; }
    public decimal TotalEarned { get; set; }
    public decimal CurrentlyLocked { get; set; }
}
