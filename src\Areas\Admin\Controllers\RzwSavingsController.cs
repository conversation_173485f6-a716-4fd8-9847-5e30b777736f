using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.BackgroundServices;

namespace RazeWinComTr.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Route("api/admin/rzw-savings")]
    [ApiController]
    [Authorize(Policy = "AdminPolicy")]
    public class RzwSavingsController : ControllerBase
    {
        private readonly AppDbContext _context;
        private readonly IRzwSavingsService _savingsService;
        private readonly IRzwSavingsInterestService _interestService;
        private readonly IStringLocalizer<SharedResource> _localizer;
        private readonly ILogger<RzwSavingsController> _logger;
        private readonly IServiceScopeFactory _serviceScopeFactory;

        public RzwSavingsController(
            AppDbContext context,
            IRzwSavingsService savingsService,
            IRzwSavingsInterestService interestService,
            IStringLocalizer<SharedResource> localizer,
            ILogger<RzwSavingsController> logger,
            IServiceScopeFactory serviceScopeFactory)
        {
            _context = context;
            _savingsService = savingsService;
            _interestService = interestService;
            _localizer = localizer;
            _logger = logger;
            _serviceScopeFactory = serviceScopeFactory;
        }

        /// <summary>
        /// DEBUG ENDPOINT: Manually trigger background service operations for testing
        /// This endpoint directly calls the RzwSavingsBackgroundService ProcessDailyOperationsAsync method
        /// </summary>
        [HttpGet("debug-trigger-background-operations")]
        public async Task<IActionResult> DebugTriggerBackgroundOperations()
        {
            try
            {
                _logger.LogInformation("DEBUG: Manual background operations triggered by admin");

                // Create a new scope and get the background service instance
                using var scope = _serviceScopeFactory.CreateScope();

                // Get the background service from the scope
                var backgroundService = scope.ServiceProvider.GetRequiredService<RzwSavingsBackgroundService>();

                // Call the ProcessDailyOperationsAsync method directly
                await backgroundService.ProcessDailyOperationsAsync();

                _logger.LogInformation("DEBUG: Background service operations completed successfully");

                return Ok(new
                {
                    success = true,
                    message = "Background service operations completed successfully",
                    timestamp = DateTime.UtcNow,
                    note = "Operations were executed using the actual RzwSavingsBackgroundService.ProcessDailyOperationsAsync method"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "DEBUG: Error during manual background operations");
                return BadRequest(new
                {
                    success = false,
                    message = $"Error during debug operations: {ex.Message}",
                    timestamp = DateTime.UtcNow
                });
            }
        }

        /// <summary>
        /// DEBUG: Get matured accounts status for processing
        /// </summary>
        [HttpGet("matured-accounts-status")]
        public async Task<IActionResult> GetMaturedAccountsStatus()
        {
            try
            {
                // Get matured accounts with user and plan info
                var maturedAccounts = await _context.RzwSavingsAccounts
                    .Include(s => s.User)
                    .Include(s => s.Plan)
                    .Where(s => s.Status == RzwSavingsStatus.Active && s.MaturityDate <= DateTime.UtcNow)
                    .ToListAsync();

                // Get total active accounts for context
                var totalActiveAccounts = await _context.RzwSavingsAccounts
                    .CountAsync(s => s.Status == RzwSavingsStatus.Active);

                return Ok(new
                {
                    success = true,
                    timestamp = DateTime.UtcNow,
                    maturedAccountsCount = maturedAccounts.Count,
                    totalActiveAccounts = totalActiveAccounts,
                    maturedAccounts = maturedAccounts.Select(account => new
                    {
                        id = account.Id,
                        userId = account.UserId,
                        userEmail = account.User?.Email,
                        planId = account.PlanId,
                        planName = account.Plan?.Name,
                        rzwAmount = account.RzwAmount,
                        maturityDate = account.MaturityDate,
                        daysOverdue = (DateTime.UtcNow - account.MaturityDate).Days
                    }).ToList()
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "DEBUG: Error getting matured accounts status");
                return BadRequest(new
                {
                    success = false,
                    message = $"Error getting matured accounts status: {ex.Message}",
                    timestamp = DateTime.UtcNow
                });
            }
        }

        /// <summary>
        /// DEBUG ENDPOINT: Get detailed information about savings accounts for testing
        /// </summary>
        [HttpGet("debug-savings-status")]
        public async Task<IActionResult> DebugSavingsStatus()
        {
            try
            {
                // Get all active accounts
                var activeAccounts = await _context.RzwSavingsAccounts
                    .Include(s => s.User)
                    .Include(s => s.Plan)
                    .Where(s => s.Status == RzwSavingsStatus.Active)
                    .ToListAsync();



                // Get matured accounts
                var maturedAccounts = await _savingsService.GetMaturedAccountsAsync();

                // Get recent interest payments
                var recentPayments = await _context.RzwSavingsInterestPayments
                    .Include(p => p.RzwSavingsAccount)
                    .ThenInclude(a => a.User)
                    .OrderByDescending(p => p.PaymentDate)
                    .Take(10)
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    timestamp = DateTime.UtcNow,
                    statistics = new
                    {
                        totalActiveAccounts = activeAccounts.Count,
                        maturedAccountsCount = maturedAccounts.Count,
                        recentPaymentsCount = recentPayments.Count
                    },
                    activeAccounts = activeAccounts.Select(a => new
                    {
                        id = a.Id,
                        userId = a.UserId,
                        userEmail = a.User?.Email,
                        planName = a.Plan?.Name,
                        rzwAmount = a.RzwAmount,
                        interestRate = a.InterestRate,
                        startDate = a.StartDate,
                        maturityDate = a.MaturityDate,
                        totalEarnedRzw = a.TotalEarnedRzw,
                        lastInterestDate = a.LastInterestDate,
                        daysHeld = (DateTime.UtcNow - a.StartDate).Days,
                        daysToMaturity = Math.Max(0, (a.MaturityDate - DateTime.UtcNow).Days),
                        isMatured = DateTime.UtcNow >= a.MaturityDate
                    }),
                    maturedAccounts = maturedAccounts.Select(a => new
                    {
                        id = a.Id,
                        userId = a.UserId,
                        rzwAmount = a.RzwAmount,
                        maturityDate = a.MaturityDate,
                        totalEarnedRzw = a.TotalEarnedRzw,
                        daysPastMaturity = (DateTime.UtcNow - a.MaturityDate).Days
                    }),
                    recentInterestPayments = recentPayments.Select(p => new
                    {
                        id = p.Id,
                        accountId = p.RzwSavingsAccountId,
                        userId = p.RzwSavingsAccount?.UserId,
                        userEmail = p.RzwSavingsAccount?.User?.Email,
                        rzwAmount = p.RzwAmount,
                        paymentDate = p.PaymentDate,
                        description = p.Description
                    })
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "DEBUG: Error getting savings status");
                return BadRequest(new
                {
                    success = false,
                    message = $"Error getting debug status: {ex.Message}",
                    timestamp = DateTime.UtcNow
                });
            }
        }

        /// <summary>
        /// DEBUG ENDPOINT: Manually trigger interest payment for specific account
        /// </summary>
        [HttpPost("debug-trigger-interest/{accountId}")]
        public async Task<IActionResult> DebugTriggerInterestForAccount(int accountId)
        {
            try
            {
                var account = await _context.RzwSavingsAccounts
                    .Include(s => s.User)
                    .Include(s => s.Plan)
                    .FirstOrDefaultAsync(s => s.Id == accountId && s.Status == RzwSavingsStatus.Active);

                if (account == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "Account not found or not active",
                        timestamp = DateTime.UtcNow
                    });
                }

                // Calculate daily interest
                var interestAmount = await _interestService.CalculateDailyInterestAsync(account);

                if (interestAmount <= 0)
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "No interest to pay (amount is 0 or negative)",
                        interestAmount = interestAmount,
                        timestamp = DateTime.UtcNow
                    });
                }

                // Pay interest
                var success = await _interestService.PayInterestAsync(account, interestAmount);

                return Ok(new
                {
                    success = success,
                    message = success ? "Interest payment successful" : "Interest payment failed",
                    accountId = accountId,
                    userId = account.UserId,
                    userEmail = account.User?.Email,
                    planName = account.Plan?.Name,
                    interestAmount = interestAmount,
                    previousTotalEarned = account.TotalEarnedRzw,
                    timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "DEBUG: Error triggering interest for account {AccountId}", accountId);
                return BadRequest(new
                {
                    success = false,
                    message = $"Error triggering interest: {ex.Message}",
                    timestamp = DateTime.UtcNow
                });
            }
        }

        /// <summary>
        /// DEBUG ENDPOINT: Manually trigger maturity for specific account
        /// </summary>
        [HttpPost("debug-trigger-maturity/{accountId}")]
        public async Task<IActionResult> DebugTriggerMaturityForAccount(int accountId)
        {
            try
            {
                var account = await _context.RzwSavingsAccounts
                    .Include(s => s.User)
                    .Include(s => s.Plan)
                    .FirstOrDefaultAsync(s => s.Id == accountId);

                if (account == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "Account not found",
                        timestamp = DateTime.UtcNow
                    });
                }

                var (success, message) = await _savingsService.ProcessMaturityAsync(accountId);

                return Ok(new
                {
                    success = success,
                    message = message,
                    accountId = accountId,
                    userId = account.UserId,
                    userEmail = account.User?.Email,
                    planName = account.Plan?.Name,
                    rzwAmount = account.RzwAmount,
                    totalEarnedRzw = account.TotalEarnedRzw,
                    maturityDate = account.MaturityDate,
                    wasMatured = DateTime.UtcNow >= account.MaturityDate,
                    timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "DEBUG: Error triggering maturity for account {AccountId}", accountId);
                return BadRequest(new
                {
                    success = false,
                    message = $"Error triggering maturity: {ex.Message}",
                    timestamp = DateTime.UtcNow
                });
            }
        }

        /// <summary>
        /// DEBUG ENDPOINT: Force update maturity date for testing (DANGEROUS - USE ONLY FOR TESTING)
        /// </summary>
        [HttpPost("debug-force-maturity-date/{accountId}")]
        public async Task<IActionResult> DebugForceMaturityDate(int accountId, [FromBody] DateTime newMaturityDate)
        {
            try
            {
                var account = await _context.RzwSavingsAccounts
                    .FirstOrDefaultAsync(s => s.Id == accountId && s.Status == RzwSavingsStatus.Active);

                if (account == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "Account not found or not active",
                        timestamp = DateTime.UtcNow
                    });
                }

                var originalMaturityDate = account.MaturityDate;
                account.MaturityDate = newMaturityDate;
                account.ModifiedDate = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogWarning("DEBUG: Forced maturity date change for account {AccountId}. Original: {Original}, New: {New}",
                    accountId, originalMaturityDate, newMaturityDate);

                return Ok(new
                {
                    success = true,
                    message = "Maturity date updated (TESTING ONLY)",
                    accountId = accountId,
                    originalMaturityDate = originalMaturityDate,
                    newMaturityDate = newMaturityDate,
                    isNowMatured = DateTime.UtcNow >= newMaturityDate,
                    timestamp = DateTime.UtcNow,
                    warning = "This is a dangerous operation for testing only!"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "DEBUG: Error forcing maturity date for account {AccountId}", accountId);
                return BadRequest(new
                {
                    success = false,
                    message = $"Error forcing maturity date: {ex.Message}",
                    timestamp = DateTime.UtcNow
                });
            }
        }
    }
}
