namespace RazeWinComTr.Areas.Admin.Enums;

/// <summary>
/// Types of verification that can be performed on user accounts
/// </summary>
public enum VerificationType
{
    /// <summary>
    /// Email address verification
    /// </summary>
    Email = 1,

    /// <summary>
    /// Phone number verification
    /// </summary>
    Phone = 2,

    /// <summary>
    /// Address verification
    /// </summary>
    Address = 3,

    /// <summary>
    /// Identity document verification
    /// </summary>
    Identity = 4,

    /// <summary>
    /// Bank account verification
    /// </summary>
    BankAccount = 5,

    /// <summary>
    /// Two-factor authentication setup
    /// </summary>
    TwoFactor = 6
}
