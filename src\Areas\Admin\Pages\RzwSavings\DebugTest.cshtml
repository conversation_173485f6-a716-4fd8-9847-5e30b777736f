@page
@model RazeWinComTr.Areas.Admin.Pages.RzwSavings.DebugTestModel
@using Microsoft.Extensions.Localization
@inject IStringLocalizer<SharedResource> Localizer
@{
    ViewData["Title"] = "RZW Savings Maturity Processing";
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>⏰ RZW Savings Maturity Processing</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="/Admin">Admin</a></li>
                        <li class="breadcrumb-item"><a href="/Admin/RzwSavings">RZW Savings</a></li>
                        <li class="breadcrumb-item active">Maturity Processing</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <section class="content">
        <div class="container-fluid">

            <!-- Matured Accounts Status -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card card-primary">
                        <div class="card-header">
                            <h3 class="card-title">📊 Matured Accounts Status</h3>
                            <div class="card-tools">
                                <button type="button" class="btn btn-sm btn-light" onclick="refreshStatus()">
                                    <i class="fas fa-sync"></i> Refresh
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="statusDisplay">
                                <div class="text-center">
                                    <i class="fas fa-spinner fa-spin"></i> Loading matured accounts status...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Maturity Processing -->
            <div class="row">
                <div class="col-12">
                    <div class="card card-success">
                        <div class="card-header">
                            <h3 class="card-title">⚡ Maturity Processing</h3>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">
                                Process matured savings accounts. This operation will pay interest and handle auto-renewals for accounts that have reached their maturity date.
                            </p>

                            <button type="button" class="btn btn-success btn-lg btn-block mb-3" onclick="triggerMaturityProcessing()" id="processButton">
                                <i class="fas fa-calendar-check"></i> Process Matured Accounts
                            </button>

                            <div id="processingResult"></div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </section>
</div>

<script>
// Base API URL
const apiBase = '/api/admin/rzw-savings';

// Load page on startup
document.addEventListener('DOMContentLoaded', function() {
    refreshStatus();
});

// Refresh matured accounts status
async function refreshStatus() {
    try {
        showLoading('statusDisplay');
        const response = await fetch(`${apiBase}/matured-accounts-status`);
        const data = await response.json();

        if (data.success) {
            displayMaturedAccountsStatus(data);
        } else {
            showError('statusDisplay', data.message);
        }
    } catch (error) {
        showError('statusDisplay', `Error loading status: ${error.message}`);
    }
}

// Display matured accounts status
function displayMaturedAccountsStatus(data) {
    const maturedCount = data.maturedAccountsCount || 0;
    const canProcess = maturedCount > 0;

    // Update process button state
    const processButton = document.getElementById('processButton');
    if (processButton) {
        processButton.disabled = !canProcess;
        if (!canProcess) {
            processButton.innerHTML = '<i class="fas fa-check"></i> No Matured Accounts to Process';
            processButton.className = 'btn btn-secondary btn-lg btn-block mb-3';
        } else {
            processButton.innerHTML = `<i class="fas fa-calendar-check"></i> Process ${maturedCount} Matured Account${maturedCount > 1 ? 's' : ''}`;
            processButton.className = 'btn btn-success btn-lg btn-block mb-3';
        }
    }

    const html = `
        <div class="row">
            <div class="col-md-4">
                <div class="info-box ${canProcess ? 'bg-warning' : 'bg-success'}">
                    <span class="info-box-icon"><i class="fas fa-calendar-check"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Matured Accounts</span>
                        <span class="info-box-number">${maturedCount}</span>
                        <span class="progress-description">${canProcess ? 'Ready for processing' : 'All processed'}</span>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="info-box bg-info">
                    <span class="info-box-icon"><i class="fas fa-piggy-bank"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Total Active Accounts</span>
                        <span class="info-box-number">${data.totalActiveAccounts || 0}</span>
                        <span class="progress-description">Currently earning interest</span>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="info-box bg-primary">
                    <span class="info-box-icon"><i class="fas fa-clock"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Last Updated</span>
                        <span class="info-box-number">${new Date(data.timestamp).toLocaleTimeString()}</span>
                        <span class="progress-description">${new Date(data.timestamp).toLocaleDateString()}</span>
                    </div>
                </div>
            </div>
        </div>

        ${data.maturedAccounts && data.maturedAccounts.length > 0 ? `
        <div class="mt-3">
            <h5>Matured Accounts Ready for Processing:</h5>
            <div class="table-responsive">
                <table class="table table-sm table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>User</th>
                            <th>Plan</th>
                            <th>Amount</th>
                            <th>Maturity Date</th>
                            <th>Days Overdue</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${data.maturedAccounts.map(account => `
                            <tr class="table-warning">
                                <td>${account.id}</td>
                                <td>${account.userEmail || 'N/A'}</td>
                                <td>${account.planName || 'N/A'}</td>
                                <td>${parseFloat(account.rzwAmount).toFixed(8)} RZW</td>
                                <td>${new Date(account.maturityDate).toLocaleDateString()}</td>
                                <td>${account.daysOverdue || 0} days</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        </div>
        ` : ''}
    `;

    document.getElementById('statusDisplay').innerHTML = html;
}

// Trigger maturity processing for all matured accounts
async function triggerMaturityProcessing() {
    const processButton = document.getElementById('processButton');
    if (processButton.disabled) {
        return;
    }

    if (!confirm('Are you sure you want to process all matured accounts? This will pay interest and handle auto-renewals.')) {
        return;
    }

    try {
        showLoading('processingResult');
        processButton.disabled = true;
        processButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';

        const response = await fetch(`${apiBase}/debug-trigger-background-operations`, {
            method: 'POST'
        });
        const data = await response.json();

        displayProcessingResult(data);

        // Refresh status after processing
        setTimeout(() => {
            refreshStatus();
        }, 1000);

    } catch (error) {
        showError('processingResult', `Error: ${error.message}`);
    } finally {
        processButton.disabled = false;
    }
}

// Display processing result
function displayProcessingResult(data) {
    const alertClass = data.success ? 'alert-success' : 'alert-danger';
    const icon = data.success ? 'fa-check' : 'fa-times';

    const html = `
        <div class="alert ${alertClass}">
            <h6><i class="fas ${icon}"></i> Maturity Processing Result</h6>
            <p><strong>Message:</strong> ${data.message}</p>
            <small>Timestamp: ${new Date(data.timestamp).toLocaleString()}</small>
            ${data.note ? `<p class="mt-2"><small><strong>Note:</strong> ${data.note}</small></p>` : ''}
        </div>
    `;

    document.getElementById('processingResult').innerHTML = html;
}

// Helper functions
function showLoading(elementId) {
    document.getElementById(elementId).innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</div>';
}

function showError(elementId, message) {
    document.getElementById(elementId).innerHTML = `<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> ${message}</div>`;
}

</script>
