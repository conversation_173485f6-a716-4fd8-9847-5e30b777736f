using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels.RzwDistributionReports;

namespace RazeWinComTr.Areas.Admin.Pages.RzwDistributionReports
{
    public class IndexModel : PageModel
    {
        private readonly IRzwDistributionReportsService _distributionReportsService;
        private readonly ILogger<IndexModel> _logger;

        public IndexModel(
            IRzwDistributionReportsService distributionReportsService,
            ILogger<IndexModel> logger)
        {
            _distributionReportsService = distributionReportsService;
            _logger = logger;
        }

        [BindProperty(SupportsGet = true)]
        public DateTime? StartDate { get; set; }

        [BindProperty(SupportsGet = true)]
        public DateTime? EndDate { get; set; }

        [BindProperty(SupportsGet = true)]
        public string ReportType { get; set; } = "daily"; // daily, weekly, monthly, yearly

        public RzwDistributionReportViewModel? Report { get; set; }

        public async Task OnGetAsync()
        {
            try
            {
                // Set default date range if not provided
                if (!StartDate.HasValue)
                    StartDate = DateTime.UtcNow.AddDays(-30);

                if (!EndDate.HasValue)
                    EndDate = DateTime.UtcNow;

                // Ensure end date includes the full day
                var adjustedEndDate = EndDate.Value.Date.AddDays(1).AddTicks(-1);

                _logger.LogInformation("Generating RZW distribution report for period {StartDate} to {EndDate}, ReportType: {ReportType}", 
                    StartDate, EndDate, ReportType);

                Report = await _distributionReportsService.GetDistributionReportAsync(StartDate.Value, adjustedEndDate);

                _logger.LogInformation("RZW distribution report generated successfully. Total RZW: {TotalRzw}", 
                    Report.TotalRzwDistributed);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating RZW distribution report");
                // Report will remain null, which will be handled in the view
            }
        }

        public async Task<IActionResult> OnGetExportAsync()
        {
            try
            {
                if (!StartDate.HasValue || !EndDate.HasValue)
                {
                    return BadRequest("Start date and end date are required for export");
                }

                var adjustedEndDate = EndDate.Value.Date.AddDays(1).AddTicks(-1);
                var report = await _distributionReportsService.GetDistributionReportAsync(StartDate.Value, adjustedEndDate);

                // Create CSV content
                var csv = GenerateCsvContent(report);
                var fileName = $"RZW_Distribution_Report_{StartDate.Value:yyyy-MM-dd}_to_{EndDate.Value:yyyy-MM-dd}.csv";

                return File(System.Text.Encoding.UTF8.GetBytes(csv), "text/csv", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting RZW distribution report");
                return BadRequest("Error generating export file");
            }
        }

        private string GenerateCsvContent(RzwDistributionReportViewModel report)
        {
            var csv = new System.Text.StringBuilder();
            
            // Header
            csv.AppendLine("RZW Distribution Report");
            csv.AppendLine($"Period: {report.StartDate:yyyy-MM-dd} to {report.EndDate:yyyy-MM-dd}");
            csv.AppendLine($"Generated: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC");
            csv.AppendLine();

            // Summary
            csv.AppendLine("Summary");
            csv.AppendLine("Source,Total RZW,Percentage");
            csv.AppendLine($"Savings Interest,{report.SavingsInterestDistribution.TotalRzwDistributed:F8},{report.SavingsInterestPercentage:F2}%");
            csv.AppendLine($"Referral Rewards,{report.ReferralRewardDistribution.TotalRzwDistributed:F8},{report.ReferralRewardPercentage:F2}%");
            csv.AppendLine($"Total,{report.TotalRzwDistributed:F8},100.00%");
            csv.AppendLine();

            // Daily breakdown
            if (report.DailyDistributionSummary.Any())
            {
                csv.AppendLine("Daily Distribution");
                csv.AppendLine("Date,Savings Interest RZW,Referral Reward RZW,Total RZW,Savings Payments,Referral Payments,Total Payments");
                
                foreach (var daily in report.DailyDistributionSummary)
                {
                    csv.AppendLine($"{daily.FormattedDate},{daily.SavingsInterestRzw:F8},{daily.ReferralRewardRzw:F8},{daily.TotalRzwDistributed:F8},{daily.SavingsInterestPaymentCount},{daily.ReferralRewardPaymentCount},{daily.TotalPaymentCount}");
                }
            }

            return csv.ToString();
        }
    }
}
