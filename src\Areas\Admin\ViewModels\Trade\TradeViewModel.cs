using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.ViewModels.ReferralReward;

namespace RazeWinComTr.Areas.Admin.ViewModels.Trade
{
    public class TradeViewModel
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public string UserEmail { get; set; } = string.Empty;
        public int CoinId { get; set; }
        public string CoinCode { get; set; } = string.Empty;
        public TradeType Type { get; set; }
        public decimal CoinAmount { get; set; }
        public decimal CoinRate { get; set; }
        public decimal TryAmount { get; set; }
        public decimal PreviousBalance { get; set; }
        public decimal NewBalance { get; set; }
        public decimal PreviousCoinBalance { get; set; }
        public decimal NewCoinBalance { get; set; }
        public decimal PreviousWalletBalance { get; set; }
        public decimal NewWalletBalance { get; set; }
        public DateTime CreatedDate { get; set; }
        public bool IsActive { get; set; }
        public int? ReferralRewardId { get; set; }
        public int? RzwSavingsAccountId { get; set; }

        // Referral reward details
        public ReferralRewardDetailsViewModel? ReferralReward { get; set; }

        // RZW Savings Account details
        public RzwSavingsAccount? RzwSavingsAccount { get; set; }

        // Helper property to get formatted referral reward description
        public string? GetReferralRewardDescription() => ReferralReward?.GetReferralRewardDescription();

        // Helper property to get formatted RZW savings account description
        public string? GetRzwSavingsAccountDescription()
        {
            if (!RzwSavingsAccountId.HasValue || RzwSavingsAccount == null) return null;

            var totalDays = (RzwSavingsAccount.MaturityDate - RzwSavingsAccount.StartDate).Days;
            var elapsedDays = (DateTime.UtcNow - RzwSavingsAccount.StartDate).Days;
            var remainingDays = Math.Max(0, (RzwSavingsAccount.MaturityDate - DateTime.UtcNow).Days);

            return Type switch
            {
                TradeType.RzwSavingsEarlyClosing =>
                    $"Vadeli hesap erken çekim - Geçen gün: {elapsedDays}, Kalan gün: {remainingDays}, Toplam gün: {totalDays}",
                TradeType.RzwSavingsAccountOpening =>
                    $"Vadeli hesap açılışı - Vade süresi: {totalDays} gün",
                TradeType.RzwSavingsAccountClosing =>
                    $"Vadeli hesap çekim işlemi",
                //TradeType.RzwSavingsMaturity =>
                //    $"Vadeli hesap vade dolumu - Toplam süre: {totalDays} gün",
                TradeType.RzwSavingsInterestPayment =>
                    $"Vadeli hesap faiz ödemesi",
                _ => $"Vadeli hesap işlemi"
            };
        }
    }
}
