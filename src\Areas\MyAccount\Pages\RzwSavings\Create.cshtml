@page
@model RazeWinComTr.Areas.MyAccount.Pages.RzwSavings.CreateModel
@using Microsoft.Extensions.Localization
@using RazeWinComTr.Areas.Admin.Helpers
@inject IStringLocalizer<SharedResource> Localizer
@{
    ViewData["Title"] = Localizer["Create New Savings Account"];
}

@await Html.PartialAsync("_SweetAlert2Script", Model.AlertMessage)

<!-- Content Header -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@Localizer["Create New Savings Account"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/MyAccount/Dashboard">@Localizer["Home"]</a></li>
                    <li class="breadcrumb-item"><a href="/MyAccount/RzwSavings">@Localizer["My Savings"]</a></li>
                    <li class="breadcrumb-item active">@Localizer["Create New Savings Account"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<section class="content">
    <div class="container-fluid">
        <div class="rzw-savings-create-container">

        @if (!Model.AvailablePlans.Any())
        {
            <div class="alert alert-warning">
                <h5><i class="icon fas fa-exclamation-triangle"></i> @Localizer["No Plans Available"]</h5>
                @Localizer["There are currently no savings plans available. Please check back later."]
            </div>
            return;
        }

        @if (Model.UserRzwBalance == null || Model.UserRzwBalance.AvailableRzw <= 0)
        {
            <div class="alert alert-warning">
                <h5><i class="icon fas fa-exclamation-triangle"></i> @Localizer["Insufficient Balance"]</h5>
                @Localizer["You don't have enough RZW tokens to create a savings account. Please deposit RZW tokens first."]
            </div>
            return;
        }

        <!-- Available Balance Section -->
        @if (Model.UserRzwBalance != null)
        {
            <div class="balance-info-section" data-available-balance="@Model.UserRzwBalance.AvailableRzw.ToString("F8", System.Globalization.CultureInfo.InvariantCulture)">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h5>@Localizer["Available Balance"]</h5>
                        <h3>@Model.UserRzwBalance.AvailableRzw.ToString("N8", new System.Globalization.CultureInfo("en-US")) RZW</h3>
                        <small>@Localizer["Available for new savings accounts"]</small>
                    </div>
                    <div class="col-md-4 text-right">
                        <i class="fas fa-wallet balance-icon"></i>
                    </div>
                </div>
            </div>
        }

        <!-- Form Container -->
        <form method="post" class="form-container">

            <!-- Plan Selection Section -->
            <div class="plan-selection-section">
                <h2 class="section-title">
                    <i class="fas fa-list"></i>
                    @Localizer["Select Savings Plan"]
                </h2>

                <div class="plan-cards-grid">
                    @foreach (var plan in Model.AvailablePlans)
                    {
                        <div class="plan-card" data-plan-id="@plan.Id">
                            <input class="plan-radio" type="radio"
                                   name="Input.PlanId" value="@plan.Id" id="<EMAIL>"
                                   @(Model.Input.PlanId == plan.Id ? "checked" : "")>

                            <div class="plan-card-header">
                                <h3 class="plan-name">@plan.Name</h3>
                                <div>
                                    <div class="plan-apy">@(RzwSavingsCalculationHelper.GetAPYAsFlooredPercentage(plan))% @Localizer["Annual Percentage Yield"]</div>
                                    <div class="plan-daily-rate">@((plan.InterestRate * 100).ToString("F6"))% @Localizer["Daily"]</div>
                                </div>
                            </div>

                            <div class="plan-description">@plan.Description</div>

                            <div class="plan-details">
                                <div><strong>@Localizer["Min"]:</strong> @(RzwSavingsCalculationHelper.GetMinAmountDisplayText(plan.MinRzwAmount))</div>
                                <div><strong>@Localizer["Max"]:</strong> @(RzwSavingsCalculationHelper.GetMaxAmountDisplayText(plan.MaxRzwAmount, Localizer["Unlimited"]))</div>
                                <div><strong>@Localizer["Term Duration"]:</strong> @plan.TermDuration @Localizer["Days"]</div>
                            </div>
                        </div>
                    }
                </div>

                <span asp-validation-for="Input.PlanId" class="text-danger"></span>
            </div>

            <!-- Amount Input Section -->
            <div class="amount-input-section">
                <h2 class="section-title">
                    <i class="fas fa-coins"></i>
                    @Localizer["Investment Amount"]
                </h2>

                <div class="amount-input-group">
                    <label asp-for="Input.RzwAmount" class="form-label">@Localizer["RZW Amount"]</label>
                    <div style="position: relative;">
                        <input asp-for="Input.RzwAmount" class="amount-input" id="rzwAmountInput"
                               placeholder="0.00000000" step="0.00000001" min="0.00000001" type="number">
                        <span class="amount-currency">RZW</span>
                    </div>
                    <span asp-validation-for="Input.RzwAmount" class="text-danger"></span>
                    <div class="amount-limits" id="amountLimits">
                        @Localizer["Please select a plan to see amount limits"]
                    </div>
                </div>


            </div>

            <!-- Interest Preview Section -->
            <div class="interest-preview-section">
                <h2 class="section-title">
                    <i class="fas fa-calculator"></i>
                    @Localizer["Interest Preview"]
                </h2>

                <div id="interestPreview" class="preview-container" style="display: none;">
                    <div class="preview-item">
                        <span class="preview-label">@Localizer["Initial Amount"]:</span>
                        <span class="preview-value" id="previewInitial">0.00000000 RZW</span>
                    </div>
                    <div class="preview-item">
                        <span class="preview-label">@Localizer["Daily Interest Rate"]:</span>
                        <span class="preview-value" id="previewDailyRate">0.000000%</span>
                    </div>
                    <div class="preview-item">
                        <span class="preview-label">@Localizer["Daily Interest"]:</span>
                        <span class="preview-value" id="previewDaily">0.00000000 RZW</span>
                    </div>
                    <div class="preview-item">
                        <span class="preview-label">@Localizer["Total Interest"]:</span>
                        <span class="preview-value" id="previewTotalInterest">0.00000000 RZW</span>
                    </div>
                    <div class="preview-item">
                        <span class="preview-label">@Localizer["Final Amount"]:</span>
                        <span class="preview-value" id="previewFinal">0.00000000 RZW</span>
                    </div>
                    <div class="preview-item">
                        <span class="preview-label">@Localizer["Effective APY"]:</span>
                        <span class="preview-value" id="previewApy">0.00%</span>
                    </div>
                </div>

                <div id="previewPlaceholder" class="preview-placeholder">
                    <i class="fas fa-chart-line"></i>
                    <p>@Localizer["Select a plan and enter amount to see interest preview"]</p>
                </div>

                <!-- Submit Button -->
                <div class="submit-button-container">
                    <button type="submit" class="submit-button" id="createButton" disabled>
                        <i class="fas fa-plus" style="margin-right: 8px;"></i>@Localizer["Create Savings Account"]
                    </button>
                    <div class="submit-warning">
                        @Localizer["Your RZW tokens will be locked until maturity date"]
                    </div>
                </div>
            </div>

        </form>
        </div>
    </div>
</section>

@section Styles {
    <link rel="stylesheet" href="/css/rzw-savings-create.css">
}

@section Scripts {
    <script src="/js/rzw-savings-create.js"></script>
    <script>
        // Pass server-side data to JavaScript
        window.rzwSavingsData = {
            availableBalance: @Html.Raw((Model.UserRzwBalance?.AvailableRzw ?? 0).ToString("F8", System.Globalization.CultureInfo.InvariantCulture)),
            localizedStrings: {
                minimumAmount: '@Localizer["Minimum amount"]',
                maximum: '@Localizer["Maximum"]',
                unlimited: '@Localizer["Unlimited"]'
            }
        };
    </script>
}
