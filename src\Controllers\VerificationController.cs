using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.Enums;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.ViewModels;

namespace RazeWinComTr.Controllers;

/// <summary>
/// Generic verification controller for all verification types (Email, Phone, Address, etc.)
/// </summary>
[Route("[controller]")]
public class VerificationController : Controller
{
    private readonly IVerificationService _verificationService;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly ILogger<VerificationController> _logger;

    public VerificationController(
        IVerificationService verificationService,
        IStringLocalizer<SharedResource> localizer,
        ILogger<VerificationController> logger)
    {
        _verificationService = verificationService;
        _localizer = localizer;
        _logger = logger;
    }

    /// <summary>
    /// Public endpoint for verifying tokens from emails/SMS/etc.
    /// URL: /Verification/Verify?token=xxx&type=Email
    /// </summary>
    /// <param name="token">Verification token</param>
    /// <param name="type">Verification type (Email, Phone, Address, etc.)</param>
    /// <returns>Verification result page</returns>
    [HttpGet("Verify")]
    public async Task<IActionResult> Verify(string token, string type = "Email")
    {
        if (string.IsNullOrEmpty(token))
        {
            _logger.LogWarning("Verification attempted with empty token for type: {Type}", type);
            return View("VerificationResult", CreateErrorResult(
                _localizer["Verification Failed"],
                _localizer["Invalid verification link. Please check your email and try again."]
            ));
        }

        if (!Enum.TryParse<VerificationType>(type, true, out var verificationType))
        {
            _logger.LogWarning("Invalid verification type: {Type}", type);
            return View("VerificationResult", CreateErrorResult(
                _localizer["Verification Failed"],
                _localizer["Invalid verification type."]
            ));
        }

        try
        {
            // First check if token is already verified
            var isAlreadyVerified = await _verificationService.IsTokenAlreadyVerifiedAsync(token);
            if (isAlreadyVerified)
            {
                _logger.LogInformation("Token already verified for token: {Token}, type: {Type}",
                    token[..8] + "...", verificationType);

                return View("VerificationResult", new VerificationResultViewModel
                {
                    IsSuccess = true,
                    Title = GetAlreadyVerifiedTitle(verificationType),
                    Message = GetAlreadyVerifiedMessage(verificationType),
                    VerificationType = verificationType
                });
            }

            var isVerified = await _verificationService.VerifyTokenAsync(token);

            if (isVerified)
            {
                _logger.LogInformation("Verification successful for token: {Token}, type: {Type}",
                    token[..8] + "...", verificationType);

                return View("VerificationResult", new VerificationResultViewModel
                {
                    IsSuccess = true,
                    Title = GetSuccessTitle(verificationType),
                    Message = GetSuccessMessage(verificationType),
                    VerificationType = verificationType
                });
            }
            else
            {
                _logger.LogWarning("Verification failed for token: {Token}, type: {Type}", 
                    token[..8] + "...", verificationType);

                return View("VerificationResult", CreateErrorResult(
                    _localizer["Verification Failed"],
                    GetFailureMessage(verificationType)
                ));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during verification for token: {Token}, type: {Type}", 
                token[..8] + "...", verificationType);

            return View("VerificationResult", CreateErrorResult(
                _localizer["Verification Error"],
                _localizer["An error occurred during verification. Please try again later."]
            ));
        }
    }



    #region Private Helper Methods

    private VerificationResultViewModel CreateErrorResult(string title, string message)
    {
        return new VerificationResultViewModel
        {
            IsSuccess = false,
            Title = title,
            Message = message,
            VerificationType = VerificationType.Email
        };
    }

    private string GetSuccessTitle(VerificationType type)
    {
        return type switch
        {
            VerificationType.Email => _localizer["Email Verified Successfully"],
            VerificationType.Phone => _localizer["Phone Verified Successfully"],
            VerificationType.Address => _localizer["Address Verified Successfully"],
            VerificationType.Identity => _localizer["Identity Verified Successfully"],
            VerificationType.BankAccount => _localizer["Bank Account Verified Successfully"],
            VerificationType.TwoFactor => _localizer["Two-Factor Authentication Enabled"],
            _ => _localizer["Verification Successful"]
        };
    }

    private string GetSuccessMessage(VerificationType type)
    {
        return type switch
        {
            VerificationType.Email => _localizer["Your email address has been verified successfully. You can now access all features of your account."],
            VerificationType.Phone => _localizer["Your phone number has been verified successfully."],
            VerificationType.Address => _localizer["Your address has been verified successfully."],
            VerificationType.Identity => _localizer["Your identity has been verified successfully."],
            VerificationType.BankAccount => _localizer["Your bank account has been verified successfully."],
            VerificationType.TwoFactor => _localizer["Two-factor authentication has been enabled successfully."],
            _ => _localizer["Verification completed successfully."]
        };
    }

    private string GetFailureMessage(VerificationType type)
    {
        return type switch
        {
            VerificationType.Email => _localizer["Email verification failed. The link may have expired or is invalid."],
            VerificationType.Phone => _localizer["Phone verification failed. The code may have expired or is invalid."],
            VerificationType.Address => _localizer["Address verification failed. Please try again."],
            VerificationType.Identity => _localizer["Identity verification failed. Please try again."],
            VerificationType.BankAccount => _localizer["Bank account verification failed. Please try again."],
            VerificationType.TwoFactor => _localizer["Two-factor authentication setup failed. Please try again."],
            _ => _localizer["Verification failed. Please try again."]
        };
    }

    private string GetAlreadyVerifiedTitle(VerificationType type)
    {
        return type switch
        {
            VerificationType.Email => _localizer["Email Already Verified"],
            VerificationType.Phone => _localizer["Phone Already Verified"],
            VerificationType.Address => _localizer["Address Already Verified"],
            VerificationType.Identity => _localizer["Identity Already Verified"],
            VerificationType.BankAccount => _localizer["Bank Account Already Verified"],
            VerificationType.TwoFactor => _localizer["Two-Factor Authentication Already Enabled"],
            _ => _localizer["Already Verified"]
        };
    }

    private string GetAlreadyVerifiedMessage(VerificationType type)
    {
        return type switch
        {
            VerificationType.Email => _localizer["Your email address has already been verified. No further action is required."],
            VerificationType.Phone => _localizer["Your phone number has already been verified. No further action is required."],
            VerificationType.Address => _localizer["Your address has already been verified. No further action is required."],
            VerificationType.Identity => _localizer["Your identity has already been verified. No further action is required."],
            VerificationType.BankAccount => _localizer["Your bank account has already been verified. No further action is required."],
            VerificationType.TwoFactor => _localizer["Two-factor authentication has already been enabled. No further action is required."],
            _ => _localizer["This verification has already been completed. No further action is required."]
        };
    }

    #endregion
}
