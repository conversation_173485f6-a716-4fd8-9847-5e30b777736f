﻿@using System.Globalization
@using Microsoft.AspNetCore.Mvc.Rendering
@using RazeWinComTr.Areas.Admin
@using Microsoft.Extensions.Localization
@using RazeWinComTr.Areas.Admin.Enums
@using RazeWinComTr.Areas.Admin.Helpers
@using RazeWinComTr.Areas.Admin.Services
@inject SettingService _settingService
@inject IStringLocalizer<SharedResource> L
@inject EmailVerificationService _emailVerificationService
@{
    var logoPath = await _settingService.GetValueAsync("logo_path") ?? "/public/image/GbErZcuBDF.png";
    var currentCultureName = CultureInfo.CurrentCulture.Name;

    // Create a list of language options for the dropdown
    List<SelectListItem> languages = [];
    foreach (var ci in StaticConfig.supportedCultures)
    {
        // Use only the language name without country
        string languageName = ci.NativeName.Split(' ')[0];
        languages.Add(new SelectListItem { Value = ci.Name, Text = languageName, Selected = currentCultureName == ci.Name });
    }

    // Check email verification status for authenticated users
    bool isEmailVerified = false;
    if (User?.Identity?.IsAuthenticated == true)
    {
        var userId = User.GetClaimUserId();
        if (userId.HasValue)
        {
            isEmailVerified = await _emailVerificationService.IsEmailVerifiedAsync(userId.Value);
        }
    }
}
<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="content-type" content="text/html;charset=UTF-8" />
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=8, IE=9, IE=10" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />

    <!-- Dinamik Meta Etiketleri -->
    <title>@ViewData["Title"] | @(await _settingService.GetValueAsync("site_title") ?? "RAZEWİN HOLDİNG")</title>
    <meta name="description" content="@(await _settingService.GetValueAsync("site_description") ?? "Promosyonlar")" />
    <meta name="keywords" content="@(await _settingService.GetValueAsync("site_keywords") ?? "Nasıl başlayacağını bilmeyenler için profesyonel yatırım danışmanlarımız 7 gün, 24 saat sizleri bekliyor olacak. Hemen destek alabilirsiniz.")" />
    <meta name="author" content="@(await _settingService.GetValueAsync("meta_author") ?? "")" />
    <meta name="robots" content="@(await _settingService.GetValueAsync("meta_robots") ?? "All")" />
    <meta name="robots" content="index,follow" />
    <meta name="googlebot" content="@(await _settingService.GetValueAsync("meta_googlebot") ?? "Index, Follow")">
    <meta name="rating" content="@(await _settingService.GetValueAsync("meta_rating") ?? "All")">
    <link rel="icon" href="@(await _settingService.GetValueAsync("favicon") ?? "/site/images/favicon.png")" type="image/png">

    <!-- Open Graph Meta Etiketleri -->
    <meta property="og:type" content="@(await _settingService.GetValueAsync("og_type") ?? "product")" />
    <meta property="og:site_name" content="@(await _settingService.GetValueAsync("site_title") ?? "RAZEWİN HOLDİNG")" />
    <meta property="og:locale" content="@(await _settingService.GetValueAsync("og_locale") ?? "tr_TR")" />
    <meta property="og:title" content="@(await _settingService.GetValueAsync("site_title") ?? "RAZEWİN HOLDİNG")" />
    <meta property="og:image" content="@(await _settingService.GetValueAsync("og_image") ?? $"{Context.Request.Scheme}://{Context.Request.Host}{logoPath}")" />
    <meta property="og:description" content="@(await _settingService.GetValueAsync("site_description") ?? "RAZEWIN - Kripto Para Alım Satım Platformu")" />
    <meta property="og:url" content="@(await _settingService.GetValueAsync("og_url") ?? "/index")" />

    <!-- Twitter Meta Etiketleri -->
    <meta name="twitter:card" content="@(await _settingService.GetValueAsync("twitter_card") ?? "summary")" />
    <meta property="twitter:url" content="@(await _settingService.GetValueAsync("twitter_url") ?? "/index")" />
    <meta property="twitter:title" content="@(await _settingService.GetValueAsync("site_title") ?? "RAZEWİN HOLDİNG")" />
    <meta property="twitter:description" content="@(await _settingService.GetValueAsync("site_description") ?? "Promosyonlar")" />
    <meta property="twitter:image" content="@(await _settingService.GetValueAsync("twitter_image") ?? $"{Context.Request.Scheme}://{Context.Request.Host}{logoPath}")" />
    <meta property="twitter:site" content="@(await _settingService.GetValueAsync("twitter_site") ?? "/index")" />

    <!-- Schema.org Meta Etiketleri -->
    <meta itemprop="description" content="@(await _settingService.GetValueAsync("site_description") ?? "Promosyonlar")" />
    <meta itemprop="keywords" content="@(await _settingService.GetValueAsync("site_keywords") ?? "kripto,bitcoin,ethereum,altcoin,trading")" />
    <meta itemprop="image" content="@(await _settingService.GetValueAsync("meta_image") ?? $"{Context.Request.Scheme}://{Context.Request.Host}{logoPath}")" />
    <link href="/cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.5.3/css/bootstrap.min.css" rel="stylesheet"
          type="text/css" />
    <link href="/public/front/extra/sweetalert2/sweetalert2.min.css" rel="stylesheet">
    <link href="/public/front/fxyatirim/assets/css/userfedafedafeda.css?v=1624220798" rel="stylesheet">
    <link href="/public/front/fxyatirim/assets/css/coinlist-enhanced.css" rel="stylesheet">
    <link href="/public/front/fxyatirim/assets/css/all5e1f5e1f5e1f.css?v=2" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="/public/front/fxyatirim/assets/fonts/all.css" rel="stylesheet">
    <link href="/public/front/fxyatirim/assets/css/market5e1f.css?v=2" rel="stylesheet">
    <link defer href="/fonts.googleapis.com/css2655.css?family=Poppins:300,400,500,600,700,800,900&amp;subset=latin-ext"
          rel="stylesheet">

    @await RenderSectionAsync("Styles", required: false)

    <!--Start of Tawk.to Script-->
    @{
        var javascriptCode = await _settingService.GetValueAsync("javascript");
        if (!string.IsNullOrEmpty(javascriptCode))
        {
            <script type="text/javascript">
                @Html.Raw(javascriptCode)
            </script>
        }
    }
    <!--End of Tawk.to Script-->
</head>

<body>
    <header>

        <div id="preloader">
            <div class="lds-dual-ring"></div>
        </div>

        <div class="fw headerAll nav-down">
            <div class="fw headerTopAll">
                <div class="container">
                    <div class="fw headerTop">
                        <div class="headerTopLeft">
                            <div class="headerTopLeftMenu">
                                <ul>
                                    <li>
                                        <select id="languageSelect" class="language-select" style="font-size: 12px;margin-top: 6px;padding: 3px;" onchange="changeLanguage(this.value)">
                                            @foreach (var language in languages)
                                            {
                                                <option value="@language.Value" selected="@language.Selected">@language.Text</option>
                                            }
                                        </select>
                                    </li>
                                    @{
                                        var whatsapp_number = await _settingService.GetValueAsync("whatsapp_number");
                                        var site_phone = await _settingService.GetValueAsync("site_phone");
                                    }
                                    @if (whatsapp_number != null)
                                    {
                                        <li><a href="https://wa.me/@(whatsapp_number.Replace("+", "").Replace(" ", ""))"><i class="flaticon-headset"></i>@L["Live Support"]</a></li>
                                        <li><a href="https://api.whatsapp.com/send?phone=+@(whatsapp_number.Replace("+", "").Replace(" ", ""))"><i class="flaticon-call-center-worker-with-headset"></i>@L["Call Me Back"]</a></li>
                                        <li><a href="https://api.whatsapp.com/send?phone=+@(whatsapp_number.Replace("+", "").Replace(" ", ""))" target="_blank"><i class="flaticon-whatsapp-logo"></i>@L["WhatsApp"]</a></li>


                                    }
                                    @if (site_phone != null)
                                    {
                                        <li><a href="tel:@(site_phone)"><i class="flaticon-telephone"></i> </a></li>
                                    }
                                </ul>
                            </div>
                        </div>
                        <partial name="_LoginPartial" />

                    </div>
                </div>
            </div>
            <div class="fw headerBotAll">
                <div class="container">
                    <div class="fw headerBot">
                        <div class="logo">
                            <a href="/index">
                                <img src="/public/image/GbErZcuBDF.png" />
                            </a>
                        </div>
                        <span class="responsiveMenu" style="z-index:123"><i class="fas fa-bars"></i></span>
                        <div class="headerMenu">
                            <nav>
                                <ul>
                                    <li><a href="/market">@L["Markets"]</a></li>
                                    <li><a href="/EarnMoney">@L["Earn Money"]</a></li>
                                    <li><a href="/packages">@L["Referral Packages"]</a></li>
                                    <li class="has-small-menu">
                                        <a href="#">@L["About Us"]</a>
                                        <div class="fw subMenu smallMenu">
                                            <div class="fw smallMenuBox">
                                                <ul>
                                                    <li><a href="/blog/about">@L["About Us"]</a></li>
                                                    <li><a href="/blog/UserAgreement">@L["User Agreement"]</a></li>
                                                    <li><a href="/blog/PrivacyPolicy">@L["Privacy Policy"]</a></li>
                                                    <li><a href="/blog/Forex">@L["What Is It"]</a></li>
                                                    <li><a href="/blog/Promotions">@L["Promotions"]</a></li>
                                                    <li><a href="/blog/Announcements">@L["Announcements"]</a></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </li>
                                    <li><a href="/Chart">@L["Chart"]</a></li>
                                    <li><a href="/blog/CrossRates">@L["Cross Rates"]</a></li>
                                    <li><a href="/blog/HeatMap">@L["Heat Map"]</a></li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>
    <div class="newMobile">
        <div class="fw newMobileBox">
            <div class="newMobileClose"><i class="flaticon-close"></i></div>
            <style>
                .newMobileHeader li a {
                    height: 80px; /* Fixed height for all menu items */
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                }

                .newMobileHeader .title {
                    text-align: center;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    height: 40px; /* Fixed height for the text area */
                }
            </style>
            <div class="fw newMobileHeader">
                <ul>
                    @if (User?.Identity?.IsAuthenticated == true)
                    {
                        <li class="orange">
                            <a href="@((User?.Identity?.IsAuthenticated == true && User.IsInRole(Roller.Admin.ToString())) ? "/Admin/Dashboard" : "/MyAccount/Dashboard")">
                                <span class="icon"><i class="flaticon-user-3"></i></span><span class="title">@(User?.Identity?.Name ?? string.Empty)</span>
                            </a>
                        </li>
                        <li class="orange">
                            <a href="@((User?.Identity?.IsAuthenticated == true && User.IsInRole(Roller.Admin.ToString())) ? "/Admin/Account/Logout" : "/MyAccount/Logout")">
                                <span class="icon"><i class="flaticon-close"></i></span><span class="title">@L["Logout"]</span>
                            </a>
                        </li>
                    }
                    else
                    {
                        <li class="blue">
                            <a href="/login">
                                <span class="icon"><i class="flaticon-user-3"></i></span><span class="title">@L["Login"]</span>
                            </a>
                        </li>
                        <li class="green">
                            <a href="/register">
                                <span class="icon"><i class="flaticon-add"></i></span><span class="title">@L["Register"]</span>
                            </a>
                        </li>
                    }
                </ul>
            </div>
            <div class="fw newMobileMenu">
                <nav>
                    <ul>
                        <li style="padding: 10px 15px;">
                            <select id="mobileLangSelect" class="language-select" style="width: 100%; font-size: 14px; padding: 5px;" onchange="changeLanguage(this.value)">
                                @foreach (var language in languages)
                                {
                                    <option value="@language.Value" selected="@language.Selected">@language.Text</option>
                                }
                            </select>
                        </li>
                        <li><a href="/market">@L["Markets"]</a></li>
                        <li><a href="/EarnMoney">@L["Earn Money"]</a></li>
                        <li><a href="/packages">@L["Referral Packages"]</a></li>
                        @if (User?.Identity?.IsAuthenticated == true)
                        {
                            <li><a href="@((User?.Identity?.IsAuthenticated == true && User.IsInRole(Roller.Admin.ToString())) ? "/Admin/Dashboard" : "/MyAccount/Dashboard")">@L["Dashboard"]</a></li>
                            <li><a href="@((User?.Identity?.IsAuthenticated == true && User.IsInRole(Roller.Admin.ToString())) ? "/Admin/Dashboard" : "/MyAccount/TradeHistory")">@L["My Trade History"]</a></li>
                            <li><a href="@((User?.Identity?.IsAuthenticated == true && User.IsInRole(Roller.Admin.ToString())) ? "/Admin/Dashboard" : "/MyAccount/Wallet")">@L["My Wallet"]<br />&nbsp;</a></li>
                            <li><a href="@((User?.Identity?.IsAuthenticated == true && User.IsInRole(Roller.Admin.ToString())) ? "/Admin/Package" : "/MyAccount/Packages")">@L["My Packages"]</a></li>
                            <li><a href="/MyAccount/RzwSavings">@L["My Saving Accounts"]</a></li>
                            <li><a href="@((User?.Identity?.IsAuthenticated == true && User.IsInRole(Roller.Admin.ToString())) ? "/Admin/Dashboard" : "/MyAccount/Profile")">@L["My Profile"]<br />&nbsp;</a></li>
                        }
                        <li><a href="/About">@L["About Us"]</a></li>
                        <li><a href="/blog/Forex">@L["What is Coin"]</a></li>
                        <li><a href="/blog/Promotions">@L["Promotions"]</a></li>
                        <li><a href="/blog/UserAgreement">@L["User Agreement"]</a></li>
                        <li><a href="/blog/PrivacyPolicy">@L["Privacy Policy"]</a></li>
                        <li><a href="/blog/CrossRates">@L["Cross Rates"]</a></li>
                        <li><a href="/blog/HeatMap">@L["Heat Map"]</a></li>
                    </ul>
                </nav>
            </div>
            <div class="fw newMobileButtons">
                <ul>
                    <li><a href="https://wa.me/************"><i class="flaticon-headset"></i>@L["Live Support"]</a></li>
                </ul>
            </div>
        </div>
    </div>
    <!-- TradingView Widget BEGIN -->
    <div class="tradingview-widget-container">
        <div class="tradingview-widget-container__widget"></div>
        <div class="tradingview-widget-copyright"></div>
        <script type="text/javascript" src="/s3.tradingview.com/external-embedding/embed-widget-ticker-tape.js" async>
                {
                "symbols"
            :
                [
                    {
                        "proName": "FOREXCOM:SPXUSD",
                        "title": "SP 500"
                    },
                    {
                        "proName": "FOREXCOM:NSXUSD",
                        "title": "US 100"
                    },
                    {
                        "proName": "FX_IDC:EURUSD",
                        "title": "EUR/USD"
                    },
                    {
                        "proName": "BITSTAMP:BTCUSD",
                        "title": "Bitcoin"
                    },
                    {
                        "proName": "BITSTAMP:ETHUSD",
                        "title": "Ethereum"
                    }
                ],
                    "showSymbolLogo"
            :
                true,
                    "colorTheme"
            :
                "dark",
                    "isTransparent"
            :
                false,
                    "displayMode"
            :
                "adaptive",
                    "locale"
            :
                "tr"
            }
        </script>
    </div>
    <!-- TradingView Widget END -->
    @if (User?.Identity?.IsAuthenticated == true)
    {

        <!-- User Profile Section -->
        <section class="bg-profile d-table w-100 bg-primary" style="background: url('/public/front/fxyatirim/assets/images/page-bg-blue.png') center center;background-size: cover;">
            <div class="container">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="card public-profile border-0 rounded shadow" style="z-index: 1;">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-lg-2 col-md-3 text-md-left text-center">
                                        <img src="/dist/img/user2-160x160.jpg" class="avatar avatar-large rounded-circle shadow d-block mx-auto" alt="@User.Identity.Name">
                                    </div>
                                    <div class="col-lg-10 col-md-9">
                                        <div class="row align-items-end">
                                            <div class="col-md-7 text-md-left text-left mt-4 mt-sm-0">
                                                <h3 class="title mb-2 pb-2">@User.Identity.Name</h3>
                                                <div class="d-flex align-items-center mb-2">
                                                    <small class="text-muted h6 mr-2">@User.GetClaimEmail()</small>
                                                    @if (User?.Identity?.IsAuthenticated == true)
                                                    {
                                                        @if (isEmailVerified)
                                                        {
                                                            <span class="badge badge-success ml-2" title="@L["Email Verified"]">
                                                                <i class="fas fa-check-circle"></i> @L["Verified"]
                                                            </span>
                                                        }
                                                        else
                                                        {
                                                            <span class="badge badge-warning ml-2" title="@L["Email Not Verified"]">
                                                                <i class="fas fa-exclamation-triangle"></i> @L["Not Verified"]
                                                            </span>
                                                        }
                                                    }
                                                </div>
                                                <ul class="list-inline mb-2 mt-3">
                                                    <li class="list-inline-item mr-2 pb-3">
                                                        <a href="tel:@(User?.GetClaimMobilePhone())" class="text-muted" title="Phone">
                                                            <i class="uil fa fa-phone"></i>
                                                            @(User?.GetClaimMobilePhone())
                                                        </a>
                                                    </li>
                                                    <li class="list-inline-item">
                                                        <a href="javascript:;" class="text-muted" title="Member Since">
                                                            <i class="uil fa fa-clock-o"></i>
                                                            @(User?.GetClaimUserCreationDate()?.ToLocalTime().ToString("dd.MM.yyyy HH:mm") ?? "N/A")
                                                        </a>
                                                    </li>
                                                </ul>
                                                <div class="bakiye">
                                                    @await Component.InvokeAsync("UserBalance")
                                                </div>
                                            </div>
                                            <div class="col-md-5 text-md-right sagbtnlaruser">
                                                <a href="@((User?.Identity?.IsAuthenticated == true && User.IsInRole(Roller.Admin.ToString())) ? "/Admin/Dashboard" : "/MyAccount/TradeHistory")" class="btn btn-primary m-2"><i class="flaticon-bars"></i> @L["My Trade History"]</a>
                                                <a href="@((User?.Identity?.IsAuthenticated == true && User.IsInRole(Roller.Admin.ToString())) ? "/Admin/Dashboard" : "/MyAccount/Wallet")" class="btn btn-primary m-2"><i class="flaticon-wallet"></i> @L["My Wallet"]</a>
                                                <a href="@((User?.Identity?.IsAuthenticated == true && User.IsInRole(Roller.Admin.ToString())) ? "/Admin/Dashboard" : "/MyAccount/Packages")" class="btn btn-primary m-2"><i class="flaticon-gift"></i> @L["My Packages"]</a>
                                                <a href="/MyAccount/RzwSavings" class="btn btn-info m-2"><i class="flaticon-investment"></i> @L["My Saving Accounts"]</a>
                                                <a href="@((User?.Identity?.IsAuthenticated == true && User.IsInRole(Roller.Admin.ToString())) ? "/Admin/Dashboard" : "/MyAccount/Profile")" class="btn btn-warning m-2"><i class="flaticon-user-3"></i> @L["My Profile"]</a>
                                                <a href="/MyAccount/Deposit" class="btn btn-success m-2"><i class="flaticon-money"></i> @L["Deposit"]</a>
                                                <a href="/MyAccount/Withdrawal" class="btn btn-success m-2"><i class="flaticon-money"></i> @L["Withdrawal"]</a>
                                                <a href="@((User?.Identity?.IsAuthenticated == true && User.IsInRole(Roller.Admin.ToString())) ? "/Admin/Account/Logout" : "/MyAccount/Logout")" class="btn btn-danger m-2"><i class="flaticon-close"></i> @L["Logout"]</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

    }
    @RenderBody()

    <footer>
        <div class="fw footerAll cover" style="background-image:url(/public/front/fxyatirim/assets/images/footer-bg.png)">
            <div class="fw footerTopAll wow fadeInUp">
                <div class="container">
                    <div class="fw footerTop">
                        <div class="footerQr">
                            <a href="https://razewin.com" target="_blank">
                                <img alt="razewin.com"
                                     src="/i.seadn.io/gae/nbOin2bp4UA_qpAcZsGMdvmYjmaKnoSUMH8zR8JkLWbVddPCTFVNIM-RyEfBPUY3_Qf6dhX8txt4NawCq8jlbLdCWWeUKHveljrDb6ef.png?auto=format&amp;w=1000"
                                     width="100px" />
                            </a>
                        </div>
                        <div class="footerLogo">
                            <img alt="razewin logo" src="/public/image/GbErZcuBDF.png" /><br /><span class="title">
                                RazeWin Markets LTD -
                                Türk Patent ve Marka Kurumu Tescil numarası: <a href="https://www.turkpatent.gov.tr/arastirma-yap" target="_blank">2024 043620</a>
                            </span>
                        </div>
                        <div class="footerTopMenu">
                            <ul>
                                <li>
                                    <a href="https://wa.me/************"><i class="flaticon-headset"></i>@L["Live Support"]</a>
                                </li>
                                <li><a href="tel:05077077293"><i class="flaticon-telephone"></i></a></li>
                                <li>
                                    <a href="https://api.whatsapp.com/send?phone=+************">
                                        <i class="flaticon-call-center-worker-with-headset"></i>@L["Call Me Back"]
                                    </a>
                                </li>
                                <li>
                                    <a href="https://api.whatsapp.com/send?phone=+************">
                                        <i class="flaticon-whatsapp-logo"></i>Whatsapp
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <!--.container-->
            </div>
            <!--.footerTopAll-->
            <div class="container">
                <div class="fw footerMenus wow fadeInUp">
                    <div class="footerMenuRow">
                        <div class="footerMenuCol">
                            <div class="fw footerMenuBox">
                                <div class="fw footerMenuTitle">
                                    <span class="title">RazeWin</span>
                                </div>
                                <!--.title-->
                                <div class="fw footerMenuNav">
                                    <ul>
                                        <li><a href="/blog/about">Hakkımızda</a></li>
                                        <li><a href="/blog/PrivacyPolicy">Belgelerimiz</a></li>
                                        <li><a href="/blog/PrivacyPolicy">Neden RazeWin ?</a></li>
                                        <li><a href="/blog/PrivacyPolicy">İnsan Kaynakları</a></li>
                                    </ul>
                                </div>
                                <!--.nav-->
                            </div>
                            <!--.menuBox-->
                        </div>
                        <!--.col-->
                        <div class="footerMenuCol">
                            <div class="fw footerMenuBox">
                                <div class="fw footerMenuTitle">
                                    <span class="title">Yatırım Araçları </span>
                                </div>
                                <!--.title-->
                                <div class="fw footerMenuNav">
                                    <ul>
                                        <li><a href=/index>Doviz Pariteleri</a></li>
                                        <li><a href=/index>Metaller</a></li>
                                        <li><a href=/index>Borsa Endeksleri</a></li>
                                        <li><a href=/index>Emtialar</a></li>
                                        <li><a href=/index>Kripto Paralar</a></li>
                                        <li><a href=/index>ABD Borsası</a></li>
                                        <li><a href=/index>Avrupa Borsası</a></li>
                                        <li><a href=/index>Asya Borsası</a></li>
                                        <li><a href=/index>Rusya Borsası</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="footerMenuCol">
                            <div class="fw footerMenuBox">
                                <div class="fw footerMenuTitle">
                                    <span class="title">Hesap İşlemleri</span>
                                </div>
                                <div class="fw footerMenuNav">
                                    <ul>
                                        <li><a href="/register">Hesap Açın!</a></li>
                                        <li><a href="/register">Hesabınızı Taşıyın!</a></li>
                                        <li><a href="/register">Hesap Türleri</a></li>
                                        <li><a href="/register">Ürün Özellikleri</a></li>
                                        <li><a href="/register">İşlem Saatleri</a></li>
                                        <li><a href="/">Canlı Fiyat Akışı</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="footerMenuCol">
                            <div class="fw footerMenuBox">
                                <div class="fw footerMenuTitle">
                                    <span class="title">Platformlar</span>
                                </div>
                                <div class="fw footerMenuNav">
                                    <ul>
                                        <li><a href="https://razewin.com">razewin.com</a></li>
                                        <li><a href="https://razewin.com">razewin.net</a></li>
                                        <li><a href="https://razewin.com">razewin.shop</a></li>
                                        <li><a href="https://razewin.com">razewins.com</a></li>

                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="footerMenuCol">
                            <div class="fw footerMenuBox">
                                <div class="fw footerMenuTitle">
                                    <span class="title">Bilgi Bankası</span>
                                </div>
                                <div class="fw footerMenuNav">
                                    <ul>
                                        <li><a href="/blog/Forex">Coin Nedir</a></li>
                                        <li><a href="/register">Sıkça Sorulanlar!</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="fw footerRiskAll wow fadeInUp">
                <div class="container">
                    <div class="fw footerRisk" style=" margin-bottom:35px; ">
                        <i class="flaticon-info onlyBlue"
                           style="background-image: linear-gradient(90deg,#0096ec 0,#118ed6 100%)!important;"></i>
                        <span class="title">
                            Bu sitede yer alan tüm içerik ve hizmetler telif haklarına tabidir ve tüm hakları "RazeWin Markets
                            Limited" firmasına aittir. İşbu web sitesinin herhangi içeriğinin kullanımı "RazeWin Markets Limited"
                            firmasının resmi temsilcisi tarafından onaylanmalı ve orijinal kaynağa ilgili internet bağlantısını
                            içermelidir. Herhangi "Çevrimiçi - brokerlik" veya "Çevrimiçi - Coin" tipi üçüncü taraf şirketler işbu
                            web sitesinde yer alan içeriği ve "RazeWin Markets Limited" firmasına ait değiştirilmiş yazıları
                            kullanma hakkına sahip değildir. İhlal halinde bu şahıslar fikri mülkiyetin korunması kanunu kapsamında
                            yargılanacaktır.
                            <br /><br />
                            RazeWin Markets Limited - Türk Patent ve Marka Kurumu tarafından denetlenmektedir (Tescil numarası <a href="https://www.turkpatent.gov.tr/arastirma-yap" target="_blank">
                                2024 043620
                            </a> )
                            <br />
                            Bu web sitesindeki tüm aracılık faaliyetleri RazeWin Markets Limited tarafından sağlanmaktadır.
                            Almanya, ABD'de yaşayanlar için hizmet mevcut değildir.
                        </span>
                    </div>
                    <div class="fw footerRisk">
                        <i class="flaticon-exclamation"></i>
                        <span class="title">
                            Risk Uyarısı:<br />Kaldıraçlı işlem gören tüm finansal ürünler, sermayeniz için yüksek
                            derecede risk taşır. Bunlar tüm yatırımcılara uygun değildir.Lütfen riskleri tamamen anladığınızdan emin
                            olun ve gerekirse bağımsız tavsiyeler isteyin. Daha ayrıntılı bilgi için tam Risk Uyarısı ve İş
                            Şartları&#039;na bakın.
                        </span>
                    </div>
                </div>
            </div>
            <div class="container">
                <div class="fw footerOtherMenu wow fadeInUp">
                    <ul>
                        <li><a href="/blog/UserAgreement">@L["Terms and Conditions"]</a></li>
                        <li><a href="/blog/PrivacyPolicy">@L["Privacy Agreement"]</a></li>
                        <li><a href="/blog/PrivacyPolicy">@L["Risk Disclosure"]</a></li>
                        <li><a href="/blog/PrivacyPolicy">@L["Anti Money Laundering Policy"]</a></li>
                    </ul>
                </div>
                <div class="fw footerIcons">
                    <ul>
                        <li data-toggle="tooltip" data-placement="bottom" title="Havale/Eft" class="wow zoomIn"
                            data-wow-delay="0.1s">
                            <span class="title">
                                <div class="footer_logof1"></div>
                            </span>
                        </li>
                        <li data-toggle="tooltip" data-placement="bottom" title="Papara" class="wow zoomIn"
                            data-wow-delay="0.2s">
                            <span class="title">
                                <div class="footer_logof2"></div>
                            </span>
                        </li>
                        <li data-toggle="tooltip" data-placement="bottom" title="CepBank" class="wow zoomIn"
                            data-wow-delay="0.3s">
                            <span class="title">
                                <div class="footer_logof3"></div>
                            </span>
                        </li>
                        <li data-toggle="tooltip" data-placement="bottom" title="Western Union" class="wow zoomIn"
                            data-wow-delay="0.4s">
                            <span class="title">
                                <div class="footer_logof4"></div>
                            </span>
                        </li>
                        <li data-toggle="tooltip" data-placement="bottom" title="Kredi Kartı" class="wow zoomIn"
                            data-wow-delay="0.5s">
                            <span class="title">
                                <div class="footer_logof5"></div>
                            </span>
                        </li>
                        <li data-toggle="tooltip" data-placement="bottom" title="Payoneer" class="wow zoomIn"
                            data-wow-delay="0.6s">
                            <span class="title">
                                <div class="footer_logof6"></div>
                            </span>
                        </li>
                        <li data-toggle="tooltip" data-placement="bottom" title="CPGCode" class="wow zoomIn"
                            data-wow-delay="0.7s">
                            <span class="title">
                                <div class="footer_logof7"></div>
                            </span>
                        </li>
                        <li data-toggle="tooltip" data-placement="bottom" title="Neteller" class="wow zoomIn"
                            data-wow-delay="0.8s">
                            <span class="title">
                                <div class="footer_logof8"></div>
                            </span>
                        </li>
                        <li data-toggle="tooltip" data-placement="bottom" title="Skrill" class="wow zoomIn"
                            data-wow-delay="0.9s">
                            <span class="title">
                                <div class="footer_logof9"></div>
                            </span>
                        </li>
                        <li data-toggle="tooltip" data-placement="bottom" title="WebMoney" class="wow zoomIn"
                            data-wow-delay="1.0s">
                            <span class="title">
                                <div class="footer_logof10"></div>
                            </span>
                        </li>
                        <li data-toggle="tooltip" data-placement="bottom" title="Payeer" class="wow zoomIn"
                            data-wow-delay="1.1s">
                            <span class="title">
                                <div class="footer_logof11"></div>
                            </span>
                        </li>
                        <li data-toggle="tooltip" data-placement="bottom" title="UldPay" class="wow zoomIn"
                            data-wow-delay="1.2s">
                            <span class="title">
                                <div class="footer_logof12"></div>
                            </span>
                        </li>
                        <li data-toggle="tooltip" data-placement="bottom" title="Bitcoin" class="wow zoomIn"
                            data-wow-delay="1.3s">
                            <span class="title">
                                <div class="footer_logof13"></div>
                            </span>
                        </li>
                        <li data-toggle="tooltip" data-placement="bottom" title="RocketPay" class="wow zoomIn"
                            data-wow-delay="1.4s">
                            <span class="title">
                                <div class="footer_logof14"></div>
                            </span>
                        </li>
                        <li data-toggle="tooltip" data-placement="bottom" title="Etherium" class="wow zoomIn"
                            data-wow-delay="1.5s">
                            <span class="title">
                                <div class="footer_logof15"></div>
                            </span>
                        </li>
                        <li data-toggle="tooltip" data-placement="bottom" title="Litecoin" class="wow zoomIn"
                            data-wow-delay="1.6s">
                            <span class="title">
                                <div class="footer_logof16"></div>
                            </span>
                        </li>
                        <li data-toggle="tooltip" data-placement="bottom" title="BitMoney" class="wow zoomIn"
                            data-wow-delay="1.7s">
                            <span class="title">
                                <div class="footer_logof17"></div>
                            </span>
                        </li>
                        <li data-toggle="tooltip" data-placement="bottom" title="Ascoin" class="wow zoomIn"
                            data-wow-delay="1.8s">
                            <span class="title">
                                <div class="footer_logof18"></div>
                            </span>
                        </li>
                        <li data-toggle="tooltip" data-placement="bottom" title="Redcoin" class="wow zoomIn"
                            data-wow-delay="1.9s">
                            <span class="title">
                                <div class="footer_logof19"></div>
                            </span>
                        </li>
                        <li data-toggle="tooltip" data-placement="bottom" title="Dashcoin" class="wow zoomIn"
                            data-wow-delay="2.0s">
                            <span class="title">
                                <div class="footer_logof20"></div>
                            </span>
                        </li>
                        <li data-toggle="tooltip" data-placement="bottom" title="Dogecoin" class="wow zoomIn"
                            data-wow-delay="2.1s">
                            <span class="title">
                                <div class="footer_logof21"></div>
                                >
                            </span>
                        </li>
                        <li data-toggle="tooltip" data-placement="bottom" title="İcoin" class="wow zoomIn"
                            data-wow-delay="2.2s">
                            <span class="title">
                                <div class="footer_logof22"></div>
                            </span>
                        </li>
                        <li data-toggle="tooltip" data-placement="bottom" title="Unkcoin" class="wow zoomIn"
                            data-wow-delay="2.3s">
                            <span class="title">
                                <div class="footer_logof23"></div>
                            </span>
                        </li>
                        <li data-toggle="tooltip" data-placement="bottom" title="Zcash" class="wow zoomIn"
                            data-wow-delay="2.4s">
                            <span class="title">
                                <div class="footer_logof24"></div>
                            </span>
                        </li>
                    </ul>
                </div>
                <div class="fw footerSocial">
                    @{
                        var facebookUrl = await _settingService.GetValueAsync("facebook_url");
                        var instagramUrl = await _settingService.GetValueAsync("instagram_url");
                        var twitterUrl = await _settingService.GetValueAsync("twitter_url");
                        var telegramUrl = await _settingService.GetValueAsync("telegram_url");
                    }
                    <ul>
                        @if (!string.IsNullOrEmpty(facebookUrl))
                        {
                            <li class="wow zoomIn facebook" data-wow-delay="0.3s">
                                <a target="_blank" href="@facebookUrl"><i class="fab fa-facebook-f"></i></a>
                            </li>
                        }

                        @if (!string.IsNullOrEmpty(instagramUrl))
                        {
                            <li class="wow zoomIn instagram" data-wow-delay="0.6s">
                                <a target="_blank" href="@instagramUrl"><i class="fab fa-instagram"></i></a>
                            </li>
                        }

                        @if (!string.IsNullOrEmpty(twitterUrl))
                        {
                            <li class="wow zoomIn twitter" data-wow-delay="0.9s">
                                <a target="_blank" href="@twitterUrl"><i class="fab fa-twitter"></i></a>
                            </li>
                        }

                        @if (!string.IsNullOrEmpty(telegramUrl))
                        {
                            <li class="wow zoomIn youtube" data-wow-delay="1.2s">
                                <a target="_blank" href="@telegramUrl"><i class="fab fa-telegram"></i></a>
                            </li>
                        }

                        <li class="wow zoomIn goTop" data-wow-delay="1.5s">
                            <a id="goTop" href="javascript:void(0)">
                                <i class="flaticon-arrow-pointing-to-right"></i>
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="fw footerCopyright wow fadeInUp">
                    <span class="title">Copyright @DateTime.UtcNow.ToLocalTime().Year - @(await _settingService.GetValueAsync("site_title") ?? "RAZEWIN") - TURKİYE </span>
                </div>
                <div class="col text-center">
                    <a href=/index target="_blank">
                        <img src="@logoPath" alt="fxsoftlabs"
                             style='max-width:150px ; margin-bottom: 15px; '>
                    </a>
                </div>
            </div>
        </div>
        <style>
            .fixedFooterMenu li a {
                height: 80px; /* Fixed height for all menu items */
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
            }

            .fixedFooterMenu .title {
                text-align: center;
                display: flex;
                flex-direction: column;
                justify-content: center;
                height: 40px; /* Fixed height for the text area */
            }

            /* Language selector styling */
            .language-select {
                background-color: #f2f1f8 !important;
                color: #13192e !important;
                border: 1px solid #5791ee !important;
                border-radius: 5px !important;
            }

                .language-select option {
                    background-color: #f2f1f8 !important;
                    color: #13192e !important;
                }
        </style>
        <div class="fw fixedFooterMenu">
            <ul>
                @if (User?.Identity?.IsAuthenticated == true)
                {
                    <li>
                        <a href="/market">
                            <span class="icon"><i class="flaticon-arrow textX orangeX"></i></span><span class="title">@L["Browse Markets"]</span>
                        </a>
                    </li>
                    <li>
                        <a href="@((User?.Identity?.IsAuthenticated == true && User.IsInRole(Roller.Admin.ToString())) ? "/Admin/Dashboard" : "/MyAccount/TradeHistory")">
                            <span class="icon"><i class="flaticon-bars textX orangeX"></i></span><span class="title">@L["My Trade History"]</span>
                        </a>
                    </li>
                    <li>
                        <a href="@((User?.Identity?.IsAuthenticated == true && User.IsInRole(Roller.Admin.ToString())) ? "/Admin/Dashboard" : "/MyAccount/Wallet")">
                            <span class="icon"><i class="flaticon-wallet textX orangeX"></i></span><span class="title">@L["My Wallet"]</span>
                        </a>
                    </li>
                    <li>
                        <a href="/MyAccount/RzwSavings">
                            <span class="icon"><i class="flaticon-investment textX orangeX"></i></span><span class="title">@L["My Saving Accounts"]</span>
                        </a>
                    </li>
                    <li>
                        <a href="@((User?.Identity?.IsAuthenticated == true && User.IsInRole(Roller.Admin.ToString())) ? "/Admin/Dashboard" : "/MyAccount/Profile")">
                            <span class="icon"><i class="flaticon-user-3 textX orangeX"></i></span><span class="title">@L["My Profile"]</span>
                        </a>
                    </li>
                    <li>
                        <a href="@((User?.Identity?.IsAuthenticated == true && User.IsInRole(Roller.Admin.ToString())) ? "/Admin/Account/Logout" : "/MyAccount/Logout")">
                            <span class="icon"><i class="flaticon-close textX pinkX"></i></span><span class="title">@L["Logout"]</span>
                        </a>
                    </li>
                }
                else
                {
                    <li>
                        <a href="/login">
                            <span class="icon"><i class="flaticon-user-3 textX blueX"></i></span><span class="title">@L["Login"]</span>
                        </a>
                    </li>
                    <li>
                        <a href="/register">
                            <span class="icon"><i class="flaticon-add textX greenX"></i></span><span class="title">@L["Register"]</span>
                        </a>
                    </li>
                    <li>
                        <a href="/market">
                            <span class="icon"><i class="flaticon-arrow textX orangeX"></i></span><span class="title">@L["Browse Markets"]</span>
                        </a>
                    </li>
                    <li>
                        <a href="/blog/Forex">
                            <span class="icon"><i class="flaticon-info textX orangeX"></i></span><span class="title">@L["What is Coin"]</span>
                        </a>
                    </li>
                    <li>
                        <a href="/blog/about">
                            <span class="icon"><i class="flaticon-info textX orangeX"></i></span><span class="title">@L["About Us"]</span>
                        </a>
                    </li>
                }
            </ul>
        </div>
    </footer>
    <div class="fixedModal" id="pnlAlertSuccess" style="display:none;">
        <div class="fw fixedModalBox dark">
            <div class="fw fixedModalDiv">
                <span class="icon green"><i class="flaticon-check-mark-button"></i></span>
                <span class="title">
                    Form başarıyla tarafımıza ulaşmıştır. sizinle en yakın sürede iletişime geçilecektir
                </span>
                <span class="fixedModalClose"><i class="flaticon-close"></i></span>
            </div>
            <!--.div-->
        </div>
        <!--.fixedModalBox-->
    </div>

    <!--.fixedModal-->
    <div class="fixedModal" id="pnlAlertSuccessReal" style="display:none;">
        <div class="fw fixedModalBox dark">
            <div class="fw fixedModalDiv">
                <span class="icon green"><i class="flaticon-check-mark-button"></i></span>
                <span class="title">
                    Hesap Açma talebiniz ilgili birime aktarılmıştır.En kısa sürede sizinle iletişime geçilecektir.
                </span>
                <span class="fixedModalClose"><i class="flaticon-close"></i></span>
            </div>
            <!--.div-->
        </div>
        <!--.fixedModalBox-->
    </div>

    <!--.fixedModal-->
    <div class="fixedModal" id="pnlAlertSuccessDemo" style="display:none;">
        <div class="fw fixedModalBox dark">
            <div class="fw fixedModalDiv">
                <span class="icon green"><i class="flaticon-check-mark-button"></i></span>
                <span class="title">
                    Demo Hesabınız ve Müşteri Paneliniz otomatik olarak oluşturularak tarafınıza SMS/MAİL olarak gönderilmiştir.
                </span>
                <span class="fixedModalClose"><i class="flaticon-close"></i></span>
            </div>
            <!--.div-->
        </div>
        <!--.fixedModalBox-->
    </div>

    <!--.fixedModal-->
    <div class="fixedModal" id="pnlAlertSuccessTransfer" style="display:none;">
        <div class="fw fixedModalBox dark">
            <div class="fw fixedModalDiv">
                <span class="icon green"><i class="flaticon-check-mark-button"></i></span>
                <span class="title">
                    Hesap Taşıma işleminiz işleme alınmıştır.En kısa sürede müşteri temsilciniz sizinle iletişime geçilecektir.
                </span>
                <span class="fixedModalClose"><i class="flaticon-close"></i></span>
            </div>
            <!--.div-->
        </div>
        <!--.fixedModalBox-->
    </div>

    <!--.fixedModal-->
    <div class="fixedModal" id="pnlAlertError" style="display:none;">
        <div class="fw fixedModalBox dark">
            <div class="fw fixedModalDiv">
                <span class="icon red"><i class="flaticon-cancel"></i></span>
                <span class="title">
                    Form aktarımında sorun oluştur, Lütfen daha sonra tekrar deneyiniz.
                </span>
                <span class="fixedModalClose"><i class="flaticon-close"></i></span>
            </div>
            <!--.div-->
        </div>
    </div>
    <!--.fixedModal-->
    <!--.fixedModal-->
    <div class="fixedModal" id="pnlAlertError2" style="display:none;">
        <div class="fw fixedModalBox dark">
            <div class="fw fixedModalDiv">
                <span class="icon red"><i class="flaticon-cancel"></i></span>
                <span class="title">
                    Aynı telefon numarasıyla sistemde kaydınız bulunmaktadır.Lütfen farklı bir numara ile deneyiniz veya <a style='color: #f93365;' target='_blank' href='/recover'> şifremi unuttum </a> sayfasına gidiniz.
                </span>
                <span class="fixedModalClose"><i class="flaticon-close"></i></span>
            </div>
            <!--.div-->
        </div>
    </div>
    <!--.fixedModal-->
    <!-- Modal -->

    <style>
        .modal {
            text-align: center;
        }

        @@media screen and (min-width: 768px) {
            .modal:before {
                display: inline-block;
                vertical-align: middle;
                content: " ";
                height: 100%;
            }
        }

        @@media screen and (max-width: 768px) {
            .modal:before {
                display: inline-block;
                vertical-align: middle;
                content: " ";
                height: 10%;
            }
        }

        .modal-dialog {
            display: inline-block;
            text-align: left;
            vertical-align: middle;
        }

    </style>

    <!-- line modal -->
    <div class="modal fade " id="coinAlModal" tabindex="-1" role="dialog" aria-labelledby="modalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">
                        <span aria-hidden="true">×</span><span class="sr-only"></span>
                    </button>
                </div>
                <div class="modal-body">
                    <form class="kript_islem kriptAlForm">
                        <div class="kript_title">
                            <img class="kriptImg" src="#" alt="coin logo">
                            <span class="kriptBaslik"></span><br>
                            <small>@L["Buy"]: <span class="kriptKur"></span></small>
                        </div>
                        <div class="input-group mb-10">
                            <span class="input-group-addon">@L["Amount to spend"]</span>
                            <input type="text" name="miktar" class="form-control kriptAlMiktar"
                                   placeholder="@L["Enter amount to spend"]">
                        </div>
                        <div class="mb-10">
                            <label class="quick-amount-label">@L["Quick Amount"]</label>
                            <div class="quick-amount-row">
                                <button type="button" class="quick-amount-btn" data-percentage="25">25%</button>
                                <button type="button" class="quick-amount-btn" data-percentage="50">50%</button>
                                <button type="button" class="quick-amount-btn" data-percentage="75">75%</button>
                                <button type="button" class="quick-amount-btn" data-percentage="100">100%</button>
                            </div>
                        </div>
                        <div class="input-group mb-10">
                            <span class="input-group-addon">@L["Amount to buy"]</span>
                            <input type="text" class="form-control kriptAlToplam" disabled placeholder="0">
                        </div>
                        <span class="kriptAlDurum"></span>
                        <p>@L["Available Balance"]: <span class="font-weight-600 kriptAlBakiye"></span></p>

                        <input type="hidden" name="coin" class="kriptCoin" value="">
                        <button type="submit" class="btn btn-block btn-success mt-20 kriptAlBtn disabled">
                            <span>@L["Submit Buy Order"]</span>
                        </button>
                    </form>

                </div>

            </div>
        </div>
    </div>
    <!-- line modal -->
    <div class="modal fade " id="coinSatModal" tabindex="-1" role="dialog" aria-labelledby="modalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">
                        <span aria-hidden="true">×</span><span class="sr-only"></span>
                    </button>
                </div>
                <div class="modal-body">
                    <form class="kript_islem kriptSatForm">
                        <div class="kript_title">
                            <img alt="coin logo" class="kriptImg" src="#" Satt="coin logo">
                            <span class="kriptBaslik"></span><br>
                            <small>@L["Sell"]: <span class="kriptKur"></span></small>
                        </div>
                        <div class="input-group mb-10">
                            <span class="input-group-addon">@L["Amount to sell"]</span>
                            <input type="text" name="miktar" class="form-control kriptSatMiktar"
                                   placeholder="@L["Enter amount to sell"]">
                        </div>
                        <div class="mb-10">
                            <label class="quick-amount-label">@L["Quick Amount"]</label>
                            <div class="quick-amount-row">
                                <button type="button" class="quick-amount-btn" data-percentage="25">25%</button>
                                <button type="button" class="quick-amount-btn" data-percentage="50">50%</button>
                                <button type="button" class="quick-amount-btn" data-percentage="75">75%</button>
                                <button type="button" class="quick-amount-btn" data-percentage="100">100%</button>
                            </div>
                        </div>
                        <div class="input-group mb-10">
                            <span class="input-group-addon">@L["Amount to Receive"]</span>
                            <input type="text" class="form-control kriptSatToplam" disabled placeholder="0">
                        </div>
                        <span class="kriptSatDurum"></span>
                        <p>@L["Available Balance"]: <span class="font-weight-600 kriptSatBakiye"></span></p>

                        <input type="hidden" name="coin" class="kriptCoin" value="">
                        <button type="submit" class="btn btn-block btn-danger mt-20 kriptSatBtn disabled">
                            <span>@L["Submit Sell Order"]</span>
                        </button>
                    </form>

                </div>

            </div>
        </div>
    </div>
    <script type="text/javascript">
        // Global translations object for multilanguage support
        window.t = {
            // Currency
            "Currency_Symbol": "@L["Currency_Symbol"]",

            // Success messages
            "Success": "@Html.Raw(L["Success"].Value)",

            // Button texts
            "OK": "@Html.Raw(L["OK"].Value)",

            // Form labels and buttons
            "Amount to spend": "@Html.Raw(L["Amount to spend"].Value)",
            "Amount to sell": "@Html.Raw(L["Amount to sell"].Value)",
            "Amount to Receive": "@Html.Raw(L["Amount to Receive"].Value)",
            "Amount to Buy": "@Html.Raw(L["Amount to Buy"].Value)",
            "Enter amount to buy": "@Html.Raw(L["Enter amount to buy"].Value)",
            "Enter amount to sell": "@Html.Raw(L["Enter amount to sell"].Value)",
            "Enter amount to spend": "@Html.Raw(L["Enter amount to spend"].Value)",
            "Available Balance": "@Html.Raw(L["Available Balance"].Value)",
            "Submit Buy Order": "@Html.Raw(L["Submit Buy Order"].Value)",
            "Submit Sell Order": "@Html.Raw(L["Submit Sell Order"].Value)",

            // Transaction labels
            "Buy": "@Html.Raw(L["Buy"].Value)",
            "Sell": "@Html.Raw(L["Sell"].Value)",

            // UI Elements
            "Theme Selector": "@Html.Raw(L["Theme Selector"].Value)",
            "Quick Amount": "@Html.Raw(L["Quick Amount"].Value)",

            // Error messages
            "Please login to make Buy/Sell transactions": "@Html.Raw(L["Please login to make Buy/Sell transactions"].Value)",

            // Validation messages
            "Please enter a valid amount": "@Html.Raw(L["Please enter a valid amount"].Value)",
            "Please enter a valid number": "@Html.Raw(L["Please enter a valid number"].Value)",
            "Amount must be at least 0.********": "@Html.Raw(L["Amount must be at least 0.********"].Value)",
            "Amount cannot exceed 1,000,000": "@Html.Raw(L["Amount cannot exceed 1,000,000"].Value)",
            "Please enter account holder name": "@Html.Raw(L["Please enter account holder name"].Value)",
            "Account holder name cannot exceed 100 characters": "@Html.Raw(L["Account holder name cannot exceed 100 characters"].Value)",
            "Please enter IBAN": "@Html.Raw(L["Please enter IBAN"].Value)",
            "IBAN cannot exceed 50 characters": "@Html.Raw(L["IBAN cannot exceed 50 characters"].Value)",
            "Please enter a valid IBAN": "@Html.Raw(L["Please enter a valid IBAN"].Value)",
            "Please enter a valid format": "@Html.Raw(L["Please enter a valid format"].Value)",
            "Enter integer part": "@Html.Raw(L["Enter integer part"].Value)",
            "Enter decimal part": "@Html.Raw(L["Enter decimal part"].Value)",
            "Please enter only digits for decimal part": "@Html.Raw(L["Please enter only digits for decimal part"].Value)",
            "Insufficient balance for this withdrawal": "@Html.Raw(L["Insufficient balance for this withdrawal"].Value)",
            "Withdrawal request submitted successfully": "@Html.Raw(L["Withdrawal request submitted successfully"].Value)",
            "Error": "@Html.Raw(L["Error"].Value)"
        };

    </script>
    <script type="text/javascript" src="/public/front/fxyatirim/assets/js/first.js"></script>
    <script type="text/javascript" src="/public/front/fxyatirim/assets/js/second.js"></script>
    <script type="text/javascript" src="/public/front/extra/sweetalert2/sweetalert2.min.js"></script>
    <script type="text/javascript" src="/public/front/extra/jquery.mask.js"></script>
    <script type="text/javascript" src="/public/front/fxyatirim/assets/js/coinlist-theme.js"></script>
    <style>
        .highlight {
            background-color: yellow;
            transition: background-color 1s ease;
        }

        /* Quick Amount Selector Styling */
        .quick-amount-row {
            display: flex;
            gap: 5px;
            margin-bottom: 15px;
        }

        .quick-amount-label {
            font-size: 14px;
            margin-bottom: 5px;
            color: #555;
            display: block;
        }

        .quick-amount-btn {
            flex: 1;
            padding: 5px 0;
            font-size: 12px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .quick-amount-btn:hover {
            background-color: #e9ecef;
        }

        .quick-amount-btn.active {
            background-color: #dc3545;
            color: white;
            border-color: #dc3545;
        }

        /* SweetAlert2 Custom Animations */
        .animated {
            animation-duration: 0.5s;
            animation-fill-mode: both;
        }

        @@keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translate3d(0, -30px, 0);
            }

            to {
                opacity: 1;
                transform: translate3d(0, 0, 0);
            }
        }

        .fadeInDown {
            animation-name: fadeInDown;
        }

        /* SweetAlert2 Custom Styling */
        .swal2-popup {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .swal2-title {
            font-size: 1.8em;
            font-weight: 600;
            color: #333;
        }

        .swal2-content {
            font-size: 1.2em;
            color: #555;
        }

        .swal2-icon.swal2-success {
            border-color: #28a745;
            color: #28a745;
        }

            .swal2-icon.swal2-success [class^=swal2-success-line] {
                background-color: #28a745;
            }

            .swal2-icon.swal2-success .swal2-success-ring {
                border-color: rgba(40, 167, 69, 0.3);
            }

        .btn-success {
            background-color: #28a745 !important;
            border-color: #28a745 !important;
        }

        .btn-lg {
            padding: 0.5rem 1.5rem;
            font-size: 1.1rem;
            border-radius: 0.3rem;
        }
    </style>
    <script>
        coinlist_veriler();

        setInterval(function () {
            coinlist_veriler();
        }, 3000);

        function coinlist_veriler() {
            $.ajax({
                url: '/api/ajax/coinlist',
                type: 'GET',
                success: function (data) {
                    data.forEach(function (coin) {
                        const row = document.querySelector(`tr[data-coin-id="${coin.Id}"]`);
                        if (row) {
                            const buyPriceCell = row.querySelector('.buyPrice');
                            const sellPriceCell = row.querySelector('.sellPrice');
                            const change24hCell = row.querySelector('.change24h');

                            // Check if the price cells have the new structure with integer-part and decimal-part spans
                            const buyPriceIntegerPart = buyPriceCell.querySelector('.integer-part');
                            const buyPriceDecimalPart = buyPriceCell.querySelector('.decimal-part');
                            const sellPriceIntegerPart = sellPriceCell.querySelector('.integer-part');
                            const sellPriceDecimalPart = sellPriceCell.querySelector('.decimal-part');

                            if (buyPriceIntegerPart && buyPriceDecimalPart && sellPriceIntegerPart && sellPriceDecimalPart) {
                                // Check if buy price is 0
                                if (parseFloat(coin.BuyPrice) === 0) {
                                    // If the content is not already "-", update it
                                    if (buyPriceCell.innerHTML !== '<span>-</span>') {
                                        buyPriceCell.innerHTML = '<span>-</span>';
                                        highlightUpdate(buyPriceCell);
                                    }
                                } else {
                                    // Split the formatted price into integer and decimal parts
                                    const buyPriceParts = coin.BuyPriceFormatted.split('.');
                                    const buyPriceIntegerFormatted = parseInt(buyPriceParts[0]).toLocaleString();
                                    const buyPriceDecimalFormatted = buyPriceParts.length > 1 ? '.' + buyPriceParts[1] : '';

                                    // Update the integer and decimal parts separately
                                    if (buyPriceIntegerPart.textContent !== buyPriceIntegerFormatted) {
                                        buyPriceIntegerPart.textContent = buyPriceIntegerFormatted;
                                        highlightUpdate(buyPriceCell);
                                    }

                                    if (buyPriceDecimalPart.textContent !== buyPriceDecimalFormatted) {
                                        buyPriceDecimalPart.textContent = buyPriceDecimalFormatted;
                                        highlightUpdate(buyPriceCell);
                                    }
                                }

                                // Check if sell price is 0
                                if (parseFloat(coin.SellPrice) === 0) {
                                    // If the content is not already "-", update it
                                    if (sellPriceCell.innerHTML !== '<span>-</span>') {
                                        sellPriceCell.innerHTML = '<span>-</span>';
                                        highlightUpdate(sellPriceCell);
                                    }
                                } else {
                                    // Split the formatted price into integer and decimal parts
                                    const sellPriceParts = coin.SellPriceFormatted.split('.');
                                    const sellPriceIntegerFormatted = parseInt(sellPriceParts[0]).toLocaleString();
                                    const sellPriceDecimalFormatted = sellPriceParts.length > 1 ? '.' + sellPriceParts[1] : '';

                                    // Update the integer and decimal parts separately
                                    if (sellPriceIntegerPart.textContent !== sellPriceIntegerFormatted) {
                                        sellPriceIntegerPart.textContent = sellPriceIntegerFormatted;
                                        highlightUpdate(sellPriceCell);
                                    }

                                    if (sellPriceDecimalPart.textContent !== sellPriceDecimalFormatted) {
                                        sellPriceDecimalPart.textContent = sellPriceDecimalFormatted;
                                        highlightUpdate(sellPriceCell);
                                    }
                                }
                            } else {
                                // Fallback for old structure (if any)
                                if (parseFloat(coin.BuyPrice) === 0) {
                                    if (buyPriceCell.textContent !== '-') {
                                        buyPriceCell.textContent = '-';
                                        highlightUpdate(buyPriceCell);
                                    }
                                } else {
                                    let newBuyPriceText = coin.BuyPriceFormatted + ' ' + window.t["Currency_Symbol"];
                                    if (buyPriceCell.textContent !== newBuyPriceText) {
                                        buyPriceCell.textContent = newBuyPriceText;
                                        highlightUpdate(buyPriceCell);
                                    }
                                }

                                if (parseFloat(coin.SellPrice) === 0) {
                                    if (sellPriceCell.textContent !== '-') {
                                        sellPriceCell.textContent = '-';
                                        highlightUpdate(sellPriceCell);
                                    }
                                } else {
                                    let newSellPriceText = coin.SellPriceFormatted + ' ' + window.t["Currency_Symbol"];
                                    if (sellPriceCell.textContent !== newSellPriceText) {
                                        sellPriceCell.textContent = newSellPriceText;
                                        highlightUpdate(sellPriceCell);
                                    }
                                }
                            }
                            try {
                                if (coin.Change24h){
                                    let newChange24hCellText = coin.Change24h.toLocaleString('tr-TR') + '%';
                                    if (change24hCell.textContent !== newChange24hCellText) {
                                        change24hCell.textContent = newChange24hCellText;
                                        highlightUpdate(change24hCell);
                                    }

                                    change24hCell.classList.remove('text-success', 'text-danger');
                                    change24hCell.classList.add(coin.Change24h >= 0 ? 'text-success' : 'text-danger');
                                }else{
                                    if (change24hCell.textContent !== '') {
                                        change24hCell.textContent = '';
                                        highlightUpdate(change24hCell);
                                    }
                                }

                            } catch (e) {
                                console.log(e);
                            }


                        }
                    });
                },
                error: function (xhr, status, error) {
                    console.error('Veri güncellenirken hata oluştu:', error);
                }
            });
        }

        function highlightUpdate(element) {
            element.classList.add('highlight');
            setTimeout(() => {
                element.classList.remove('highlight');
            }, 500);
        }
    </script>

    <script>
        $(window).on('load', function () {
            showOnceInADay();
        });
    </script>

    <script>
        const hata_goster = (type, text) => {
            if (type == "success") {
                Swal.fire({
                    position: "top-end",
                    type: "success",
                    title: text,
                    showConfirmButton: !1,

                })
            } else {
                Swal.fire({
                    html: text,
                    type: "error",
                    confirmButtonColor: "#556ee6"
                })
            }
        }

        $(".ajaxForm").submit(function (e) {
            var form = $(this);
            var formData = new FormData(form[0]);

            e.preventDefault();
            $.ajax({
                url: $(this).attr("action"),
                method: "POST",
                dataType: "JSON",
                data: formData,
                processData: false,
                contentType: false,
                success: function (data) {
                    console.log(data);
                    hata_goster(data.durum, data.mesaj);
                }
            })
        });
    </script>

    @* <script>

    $(document).on('click', '.coinAlBtn', function (e) {
        e.preventDefault();

        hata_goster('error', '@L["Please login to make Buy/Sell transactions"]');
    });

    $(document).on('click', '.coinSatBtn', function (e) {
        e.preventDefault();

        hata_goster('error', '@L["Please login to make Buy/Sell transactions"]');
    });

</script> *@




    @await RenderSectionAsync("Scripts", required: false)

    <script type="text/javascript">
        function changeLanguage(language) {
            // Get the current page's relative URL (path + query string)
            const currentUrl = window.location.pathname + window.location.search;

            // Use the main SetLanguage page for all areas
            window.location.href = `/SetLanguage?lang=${language}&returnUrl=${encodeURIComponent(currentUrl)}`;
        }

        // Set the selected language based on the current culture
        document.addEventListener('DOMContentLoaded', function() {
            const currentCulture = '@currentCultureName';
            const languageSelect = document.getElementById('languageSelect');
            const mobileLangSelect = document.getElementById('mobileLangSelect');

            if (languageSelect) {
                languageSelect.value = currentCulture;
            }

            if (mobileLangSelect) {
                mobileLangSelect.value = currentCulture;
            }
        });
    </script>
</body>


</html>
