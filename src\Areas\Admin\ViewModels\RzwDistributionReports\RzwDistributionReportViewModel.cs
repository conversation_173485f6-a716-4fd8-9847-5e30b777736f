namespace RazeWinComTr.Areas.Admin.ViewModels.RzwDistributionReports
{
    /// <summary>
    /// Main view model for RZW distribution reports
    /// Contains comprehensive data about RZW distributions from all sources
    /// </summary>
    public class RzwDistributionReportViewModel
    {
        /// <summary>
        /// Start date of the report period
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// End date of the report period
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// Total RZW distributed across all sources
        /// </summary>
        public decimal TotalRzwDistributed { get; set; }

        /// <summary>
        /// Savings interest distribution data
        /// </summary>
        public SavingsInterestDistributionViewModel SavingsInterestDistribution { get; set; } = new();

        /// <summary>
        /// Referral reward distribution data
        /// </summary>
        public ReferralRewardDistributionViewModel ReferralRewardDistribution { get; set; } = new();

        /// <summary>
        /// Daily distribution summary for the period
        /// </summary>
        public List<DailyDistributionSummaryViewModel> DailyDistributionSummary { get; set; } = new();

        /// <summary>
        /// Weekly distribution summary for the period
        /// </summary>
        public List<WeeklyDistributionSummaryViewModel> WeeklyDistributionSummary { get; set; } = new();

        /// <summary>
        /// Monthly distribution summary for the period
        /// </summary>
        public List<MonthlyDistributionSummaryViewModel> MonthlyDistributionSummary { get; set; } = new();

        /// <summary>
        /// Yearly distribution summary for the period
        /// </summary>
        public List<YearlyDistributionSummaryViewModel> YearlyDistributionSummary { get; set; } = new();

        /// <summary>
        /// Percentage of total distribution from savings interest
        /// </summary>
        public decimal SavingsInterestPercentage => TotalRzwDistributed > 0 
            ? Math.Round((SavingsInterestDistribution.TotalRzwDistributed / TotalRzwDistributed) * 100, 2) 
            : 0;

        /// <summary>
        /// Percentage of total distribution from referral rewards
        /// </summary>
        public decimal ReferralRewardPercentage => TotalRzwDistributed > 0 
            ? Math.Round((ReferralRewardDistribution.TotalRzwDistributed / TotalRzwDistributed) * 100, 2) 
            : 0;
    }
}
