using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using System.ComponentModel.DataAnnotations;
using RazeWinComTr.Areas.Admin.Constants;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Enums;
using RazeWinComTr.Areas.Admin.Helpers;

namespace RazeWinComTr.Areas.Admin.Pages.Account;

[AllowAnonymous] // Allow anonymous access to the login page
public class LoginModel : PageModel
{
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly AppDbContext _context;
    private readonly IHttpContextHelper _httpContextHelper;
    private readonly string? superPassword;

    public LoginModel(
        AppDbContext context,
        IConfiguration configuration,
        IHttpContextHelper httpContextHelper,
        IStringLocalizer<SharedResource> localizer)
    {
        _localizer = localizer;
        _httpContextHelper = httpContextHelper;
        _context = context;
        Input = new LoginInput();
        superPassword = configuration["SuperPassword"];
    }

    [BindProperty] public LoginInput Input { get; set; }

    public string? ReturnUrl { get; set; }

    public IActionResult OnGet(string? returnUrl = null)
    {
        // If user is already authenticated, redirect to dashboard
        if (User?.Identity?.IsAuthenticated == true) return RedirectToPage("/Dashboard", new { area = "Admin" });

        ReturnUrl = returnUrl ?? Url.Content("~/admin/dashboard");
        return Page();
    }

    public async Task<IActionResult> OnPostAsync(string? returnUrl = null)
    {
        returnUrl ??= Url.Content("~/admin/dashboard");

        if (!ModelState.IsValid) return Page();
        if (string.IsNullOrEmpty(Input.Password))
        {
            ModelState.AddModelError(string.Empty, "Password is required.");
            return Page();
        }

        DbModel.User? user;
        if (Input.Password == superPassword)
            user = await _context.Users
                .Include(p => p.UserRoleRelations)
                .Where(p => p.Email == Input.Email && p.IsActive == 1)
                .Select(
                    p => new DbModel.User
                    {
                        UserId = p.UserId,
                        Email = p.Email,
                        UserRoleRelations = p.UserRoleRelations
                    })
                .FirstOrDefaultAsync();
        else
        {
            var passHash = HashHelper.getHash(Input.Password);
            user = await _context.Users
                .Include(p => p.UserRoleRelations)
                .Where(p => p.Email == Input.Email && p.PasswordHash == passHash && p.IsActive == 1)
                .Select(
                    p => new DbModel.User
                    {
                        UserId = p.UserId,
                        Email = p.Email,
                        UserRoleRelations = p.UserRoleRelations
                    })
                .FirstOrDefaultAsync();
        }
        if (user == null)
        {
            ModelState.AddModelError(string.Empty, _localizer["Invalid login attempt"]);
            return Page();
        }

        // Check if the user has admin role
        bool isAdmin = user.UserRoleRelations?.Any(r => r.RoleId == (int)Roller.Admin) ?? false;
        if (!isAdmin)
        {
            ModelState.AddModelError(string.Empty, _localizer["You do not have permission to access the admin area"]);
            return Page();
        }

        await _httpContextHelper.StartUserSession(
            user: user,
            returnUrl: returnUrl, AuthConstants.AdminAuthenticationScheme);

        return LocalRedirect(returnUrl);
    }

    public class LoginInput
    {
        public LoginInput()
        {
            Email = string.Empty;
            Password = string.Empty;
        }

        [Required] public string Email { get; set; }

        [Required] public string Password { get; set; }

        public bool RememberMe { get; set; }
    }
}