using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Enums;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Models;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using System.Text.RegularExpressions;
using Xunit;

namespace RazeWinComTr.Tests.Integration;

/// <summary>
/// Integration tests for the complete email verification flow:
/// User registration → Email sending → Email verification → Profile page verification status
/// </summary>
public class EmailVerificationIntegrationTests : IDisposable
{
    private readonly AppDbContext _context;
    private readonly EmailVerificationService _emailVerificationService;
    private readonly IVerificationService _verificationService;
    private readonly Mock<IEmailHelper> _mockEmailHelper;

    public EmailVerificationIntegrationTests()
    {
        var options = new DbContextOptionsBuilder<AppDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;

        _context = new AppDbContext(options);

        // Setup mock email helper
        _mockEmailHelper = new Mock<IEmailHelper>();

        // Create verification service
        var logger = new LoggerFactory().CreateLogger<VerificationService>();
        _verificationService = new VerificationService(_context, logger);

        // Create email verification service with mocked email helper
        var mockLocalizer = new Mock<IStringLocalizer<SharedResource>>();
        var mockEmailLogger = new Mock<ILogger<EmailVerificationService>>();
        var mockEnvironment = new Mock<IWebHostEnvironment>();
        var mockHttpContextAccessor = new Mock<IHttpContextAccessor>();

        SetupMocks(mockLocalizer, mockEnvironment, mockHttpContextAccessor);

        _emailVerificationService = new EmailVerificationService(
            _verificationService,
            _context,
            _mockEmailHelper.Object,
            mockLocalizer.Object,
            mockEmailLogger.Object,
            mockEnvironment.Object,
            mockHttpContextAccessor.Object);
    }

    private void SetupMocks(Mock<IStringLocalizer<SharedResource>> mockLocalizer,
        Mock<IWebHostEnvironment> mockEnvironment,
        Mock<IHttpContextAccessor> mockHttpContextAccessor)
    {
        // Setup environment
        mockEnvironment.Setup(e => e.EnvironmentName).Returns("Development");

        // Setup HttpContext for base URL
        var mockHttpContext = new Mock<HttpContext>();
        var mockRequest = new Mock<HttpRequest>();
        mockRequest.Setup(r => r.Scheme).Returns("https");
        mockRequest.Setup(r => r.Host).Returns(new HostString("localhost:5001"));
        mockHttpContext.Setup(c => c.Request).Returns(mockRequest.Object);
        mockHttpContextAccessor.Setup(a => a.HttpContext).Returns(mockHttpContext.Object);

        // Setup localizer
        mockLocalizer.Setup(l => l["Email Verification Required"])
            .Returns(new LocalizedString("Email Verification Required", "Email Verification Required"));
        mockLocalizer.Setup(l => l["Hello"])
            .Returns(new LocalizedString("Hello", "Hello"));
        mockLocalizer.Setup(l => l["Thank you for registering with RazeWin. To complete your registration, please verify your email address by clicking the button below:"])
            .Returns(new LocalizedString("Thank you for registering with RazeWin. To complete your registration, please verify your email address by clicking the button below:", "Thank you for registering with RazeWin. To complete your registration, please verify your email address by clicking the button below:"));
        mockLocalizer.Setup(l => l["Verify Email Address"])
            .Returns(new LocalizedString("Verify Email Address", "Verify Email Address"));
        mockLocalizer.Setup(l => l["If the button doesn't work, you can copy and paste this link into your browser:"])
            .Returns(new LocalizedString("If the button doesn't work, you can copy and paste this link into your browser:", "If the button doesn't work, you can copy and paste this link into your browser:"));
        mockLocalizer.Setup(l => l["This verification link will expire in 24 hours."])
            .Returns(new LocalizedString("This verification link will expire in 24 hours.", "This verification link will expire in 24 hours."));
        mockLocalizer.Setup(l => l["If you didn't create an account with us, please ignore this email."])
            .Returns(new LocalizedString("If you didn't create an account with us, please ignore this email.", "If you didn't create an account with us, please ignore this email."));
        mockLocalizer.Setup(l => l["All rights reserved."])
            .Returns(new LocalizedString("All rights reserved.", "All rights reserved."));
    }

    private void CleanDatabase()
    {
        _context.UserVerifications.RemoveRange(_context.UserVerifications);
        _context.Users.RemoveRange(_context.Users);
        _context.SaveChanges();
    }

    [Fact]
    public async Task CompleteEmailVerificationFlow_ShouldWorkEndToEnd()
    {
        // Arrange - Create a test user
        var user = new User
        {
            Email = "<EMAIL>",
            Name = "Integration",
            Surname = "Test",
            IdentityNumber = "***********",
            PhoneNumber = "+************",
            PasswordHash = "hashedpassword",
            CrDate = DateTime.UtcNow,
            IsActive = 1
        };

        _context.Users.Add(user);
        await _context.SaveChangesAsync();

        string capturedVerificationUrl = string.Empty;
        
        // Setup email helper to capture the verification URL
        _mockEmailHelper.Setup(e => e.SendEmailAsync(It.IsAny<EmailMessage>()))
            .Callback<EmailMessage>(msg => {
                // Extract verification URL from email body - try multiple patterns
                var urlMatch = Regex.Match(msg.Body, @"href='([^']*Verification/Verify[^']*)'");
                if (!urlMatch.Success)
                {
                    urlMatch = Regex.Match(msg.Body, @"href=""([^""]*Verification/Verify[^""]*)""");
                }
                if (urlMatch.Success)
                {
                    capturedVerificationUrl = urlMatch.Groups[1].Value;
                }
            })
            .ReturnsAsync(true);

        // Act 1: Send verification email
        var emailSent = await _emailVerificationService.SendVerificationEmailAsync(
            user.UserId, 
            user.Email, 
            $"{user.Name} {user.Surname}");

        // Assert 1: Email should be sent successfully
        Assert.True(emailSent);
        Assert.NotEmpty(capturedVerificationUrl);
        _mockEmailHelper.Verify(e => e.SendEmailAsync(It.IsAny<EmailMessage>()), Times.Once);

        // Act 2: Extract token from URL and verify it
        var tokenMatch = Regex.Match(capturedVerificationUrl, @"token=([^&]+)");
        Assert.True(tokenMatch.Success, "Token should be found in verification URL");
        
        var token = tokenMatch.Groups[1].Value;
        var verificationResult = await _verificationService.VerifyTokenAsync(token);

        // Assert 2: Token verification should succeed
        Assert.True(verificationResult);

        // Act 3: Check verification status
        var verificationStatus = await _emailVerificationService.GetVerificationStatusAsync(user.UserId);

        // Assert 3: User should be verified
        Assert.True(verificationStatus.IsVerified);
        Assert.NotNull(verificationStatus.VerifiedDate);
        Assert.Equal(user.Email, verificationStatus.TargetValue);
        Assert.False(verificationStatus.CanSendVerification); // Should not be able to send more emails

        // Act 4: Verify database state
        var dbVerification = await _context.UserVerifications
            .FirstOrDefaultAsync(v => v.UserId == user.UserId && v.VerificationType == VerificationType.Email);

        // Assert 4: Database should reflect verified state
        Assert.NotNull(dbVerification);
        Assert.True(dbVerification.IsVerified);
        Assert.NotNull(dbVerification.VerifiedDate);
        Assert.Equal(user.Email, dbVerification.TargetValue);
    }

    [Fact]
    public async Task EmailVerificationFlow_WithExpiredToken_ShouldFail()
    {
        // Arrange
        var user = new User
        {
            Email = "<EMAIL>",
            Name = "Expired",
            Surname = "Test",
            IdentityNumber = "12345678902",
            PhoneNumber = "+905551234568",
            PasswordHash = "hashedpassword",
            CrDate = DateTime.UtcNow,
            IsActive = 1
        };

        _context.Users.Add(user);
        await _context.SaveChangesAsync();

        // Generate token and manually expire it
        var token = await _verificationService.GenerateVerificationTokenAsync(
            user.UserId, VerificationType.Email, user.Email);

        var verification = await _context.UserVerifications
            .FirstOrDefaultAsync(v => v.VerificationToken == token);
        verification!.TokenExpiry = DateTime.UtcNow.AddHours(-1); // Expire 1 hour ago
        await _context.SaveChangesAsync();

        // Act
        var verificationResult = await _verificationService.VerifyTokenAsync(token);

        // Assert
        Assert.False(verificationResult);
        
        var verificationStatus = await _emailVerificationService.GetVerificationStatusAsync(user.UserId);
        Assert.False(verificationStatus.IsVerified);
    }

    [Fact]
    public async Task EmailVerificationFlow_RateLimiting_ShouldWork()
    {
        // Arrange
        var user = new User
        {
            Email = "<EMAIL>",
            Name = "RateLimit",
            Surname = "Test",
            IdentityNumber = "12345678903",
            PhoneNumber = "+905551234569",
            PasswordHash = "hashedpassword",
            CrDate = DateTime.UtcNow,
            IsActive = 1
        };

        _context.Users.Add(user);
        await _context.SaveChangesAsync();

        _mockEmailHelper.Setup(e => e.SendEmailAsync(It.IsAny<EmailMessage>()))
            .ReturnsAsync(true);

        // Act 1: Send first email
        var firstEmailSent = await _emailVerificationService.SendVerificationEmailAsync(
            user.UserId, user.Email, $"{user.Name} {user.Surname}");

        // Act 2: Try to send second email immediately (should be rate limited)
        var secondEmailSent = await _emailVerificationService.SendVerificationEmailAsync(
            user.UserId, user.Email, $"{user.Name} {user.Surname}");

        // Assert
        Assert.True(firstEmailSent);
        Assert.False(secondEmailSent); // Should be rate limited
        
        // Verify only one email was sent
        _mockEmailHelper.Verify(e => e.SendEmailAsync(It.IsAny<EmailMessage>()), Times.Once);

        // Check rate limiting status
        var canSend = await _emailVerificationService.CanSendVerificationEmailAsync(user.UserId);
        Assert.False(canSend);

        var timeUntilNext = await _emailVerificationService.GetTimeUntilNextEmailAsync(user.UserId);
        Assert.NotNull(timeUntilNext);
        Assert.True(timeUntilNext.Value.TotalMinutes > 0);
    }

    [Fact]
    public async Task EmailVerificationFlow_AlreadyVerified_ShouldNotAllowMoreEmails()
    {
        // Arrange
        var user = new User
        {
            Email = "<EMAIL>",
            Name = "Already",
            Surname = "Verified",
            IdentityNumber = "12345678904",
            PhoneNumber = "+905551234570",
            PasswordHash = "hashedpassword",
            CrDate = DateTime.UtcNow,
            IsActive = 1
        };

        _context.Users.Add(user);
        await _context.SaveChangesAsync();

        // First verify the user
        var token = await _verificationService.GenerateVerificationTokenAsync(
            user.UserId, VerificationType.Email, user.Email);
        await _verificationService.VerifyTokenAsync(token);

        _mockEmailHelper.Setup(e => e.SendEmailAsync(It.IsAny<EmailMessage>()))
            .ReturnsAsync(true);

        // Act: Try to send verification email to already verified user
        var emailSent = await _emailVerificationService.SendVerificationEmailAsync(
            user.UserId, user.Email, $"{user.Name} {user.Surname}");

        // Assert
        Assert.False(emailSent); // Should not send email to already verified user
        _mockEmailHelper.Verify(e => e.SendEmailAsync(It.IsAny<EmailMessage>()), Times.Never);

        var canSend = await _emailVerificationService.CanSendVerificationEmailAsync(user.UserId);
        Assert.False(canSend);
    }

    public void Dispose()
    {
        CleanDatabase();
        _context.Dispose();
    }
}
